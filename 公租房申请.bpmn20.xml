<?xml version='1.0' encoding='UTF-8'?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="rent001">
  <process id="rent_apply" name="公租房申请" isExecutable="true">
    <documentation>用于资格轮候公租房申请流程</documentation>
    <startEvent id="startEvent1" flowable:initiator="initiator"/>
    <userTask id="sb58cb8979" name="单位初审" flowable:candidateUsers="${hsQwApplyService.findUserByRoleAndOffice(officeCode,'DZ_JG')}"/>
    <userTask id="sca65c2c63" name="经办初审" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sbae041fee" name="处室领导初审" flowable:candidateGroups="HS_CSLD">
      <extensionElements>
        <modeler:group-info-name-HS_CSLD xmlns:modeler="http://flowable.org/modeler"><![CDATA[处室领导（住房保障处） (HS_CSLD)]]></modeler:group-info-name-HS_CSLD>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s17200be80" name="选择资格轮候公示名单" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s3c82cf348" name="资格轮候网上公示" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sfc1279869" name="发起配租复查" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s24f460c9d" name="配租复查确认" flowable:assignee="${initiator}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s42935f27b" name="单位配租复查审批" flowable:candidateUsers="${hsQwApplyService.findUserByRoleAndOffice(officeCode,'DZ_JG')}"/>
    <userTask id="seceb4a0b2" name="经办配租复查审批" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s79a7f1bfd" name="选择配租名单" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s85e7a7e91" name="配租公示方案审批" flowable:candidateUsers="admin">
      <extensionElements>
        <modeler:user-info-firstname-admin xmlns:modeler="http://flowable.org/modeler"><![CDATA[默认管理员 (admin)]]></modeler:user-info-firstname-admin>
        <modeler:activiti-idm-candidate-user xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-user>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sbf31c424a" name="房源配租" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sbdc38081a" name="申请人确认" flowable:assignee="${initiator}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="s1ae13dea4"/>
    <sequenceFlow id="s96d6e74f0" sourceRef="sbdc38081a" targetRef="s1ae13dea4"/>
    <userTask id="se95f4dc58" name="租赁合同上传" flowable:candidateGroups="FG_JG">
      <extensionElements>
        <modeler:group-info-name-FG_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（房管机构） (FG_JG)]]></modeler:group-info-name-FG_JG>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <endEvent id="sb7c61e7f3"/>
    <sequenceFlow id="seb4dab6dc" sourceRef="s42935f27b" targetRef="seceb4a0b2"/>
    <sequenceFlow id="sc9ffd5f86" sourceRef="sfc1279869" targetRef="s24f460c9d"/>
    <sequenceFlow id="s6087dc86a" sourceRef="s3c82cf348" targetRef="sfc1279869"/>
    <sequenceFlow id="s0af075f45" sourceRef="s17200be80" targetRef="s3c82cf348"/>
    <sequenceFlow id="s7c3c16798" sourceRef="sbae041fee" targetRef="s17200be80"/>
    <sequenceFlow id="s7a83e1182" sourceRef="sca65c2c63" targetRef="sbae041fee"/>
    <sequenceFlow id="s99f1ecf42" sourceRef="sb58cb8979" targetRef="sca65c2c63"/>
    <serviceTask id="s6f5f0b670" name="录入黑名单" flowable:class="com.jeesite.modules.bpm.delegate.BpmDefaultServiceTask"/>
    <userTask id="s5c1d74106" name="用户申请" flowable:assignee="${initiator}" flowable:skipExpression="${firstApply}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="s4af64b9ae" sourceRef="startEvent1" targetRef="s5c1d74106"/>
    <sequenceFlow id="sbca2fa5e8" sourceRef="s5c1d74106" targetRef="sb58cb8979"/>
    <sequenceFlow id="sca4faaca9" name="不承租" sourceRef="s1ae13dea4" targetRef="s6f5f0b670">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${applyAccepted!=0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sabb0f80e1" name="承租" sourceRef="s1ae13dea4" targetRef="se95f4dc58">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${applyAccepted==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sa91dc357f" sourceRef="s6f5f0b670" targetRef="sb7c61e7f3"/>
    <exclusiveGateway id="s6ea338ef0"/>
    <sequenceFlow id="s8cba1083e" sourceRef="s24f460c9d" targetRef="s6ea338ef0"/>
    <userTask id="s6bec7f205" name="不轮候申请" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="s49f1b2dff"/>
    <sequenceFlow id="s9ca0cf513" sourceRef="seceb4a0b2" targetRef="s49f1b2dff"/>
    <sequenceFlow id="s7290f05d1" sourceRef="s79a7f1bfd" targetRef="s85e7a7e91"/>
    <sequenceFlow id="sb4177df32" name="复查不参与，仅本次不参与" sourceRef="s6ea338ef0" targetRef="sfc1279869">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckStatus==1}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="sbacaa6539" name="线下配租确认" flowable:assignee="${initiator}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="s8c5eda200"/>
    <sequenceFlow id="s6f91823ca" sourceRef="sbacaa6539" targetRef="s8c5eda200"/>
    <sequenceFlow id="s02982d5bc" sourceRef="s85e7a7e91" targetRef="sbacaa6539"/>
    <sequenceFlow id="s154f88cca" name="确认参与" sourceRef="s6ea338ef0" targetRef="s42935f27b">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckStatus==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s65ed85c32" name="确认不参与，资格已不满足" sourceRef="s6ea338ef0" targetRef="s6bec7f205">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckStatus==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="seed403ef1" name="通过" sourceRef="s49f1b2dff" targetRef="s79a7f1bfd">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckAudit==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s7f3117a31" name="资格不满足" sourceRef="s49f1b2dff" targetRef="s6bec7f205">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckAudit==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s97c958549" name="材料有问题" sourceRef="s49f1b2dff" targetRef="s42935f27b">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckAudit==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sfdc2db890" name="参与线下配租" sourceRef="s8c5eda200" targetRef="sbf31c424a">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${offlineRent==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sed1d6ea77" name="不参与线下配租" sourceRef="s8c5eda200" targetRef="sfc1279869">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${offlineRent==1}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="s4a0c5a5b3" name="处室领导审核" flowable:candidateGroups="HS_CSLD">
      <extensionElements>
        <modeler:group-info-name-HS_CSLD xmlns:modeler="http://flowable.org/modeler"><![CDATA[处室领导（住房保障处） (HS_CSLD)]]></modeler:group-info-name-HS_CSLD>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="s460023125" sourceRef="s6bec7f205" targetRef="s4a0c5a5b3"/>
    <sequenceFlow id="s635d0f065" sourceRef="s4a0c5a5b3" targetRef="sb7c61e7f3"/>
    <sequenceFlow id="s77eaaf343" sourceRef="se95f4dc58" targetRef="sb7c61e7f3"/>
    <sequenceFlow id="s342838adc" sourceRef="sbf31c424a" targetRef="sbdc38081a"/>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_rent_apply">
    <bpmndi:BPMNPlane bpmnElement="rent_apply" id="BPMNPlane_rent_apply">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="180.0" y="0.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sb58cb8979" id="BPMNShape_sb58cb8979">
        <omgdc:Bounds height="80.0" width="100.0" x="360.0" y="104.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sca65c2c63" id="BPMNShape_sca65c2c63">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="104.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbae041fee" id="BPMNShape_sbae041fee">
        <omgdc:Bounds height="80.0" width="100.0" x="780.0" y="104.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s17200be80" id="BPMNShape_s17200be80">
        <omgdc:Bounds height="80.0" width="100.0" x="780.0" y="255.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s3c82cf348" id="BPMNShape_s3c82cf348">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="255.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sfc1279869" id="BPMNShape_sfc1279869">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="405.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s24f460c9d" id="BPMNShape_s24f460c9d">
        <omgdc:Bounds height="80.0" width="100.0" x="135.0" y="540.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s42935f27b" id="BPMNShape_s42935f27b">
        <omgdc:Bounds height="80.0" width="100.0" x="360.0" y="540.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="seceb4a0b2" id="BPMNShape_seceb4a0b2">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="540.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s79a7f1bfd" id="BPMNShape_s79a7f1bfd">
        <omgdc:Bounds height="80.0" width="100.0" x="780.0" y="670.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s85e7a7e91" id="BPMNShape_s85e7a7e91">
        <omgdc:Bounds height="80.0" width="100.0" x="990.0" y="670.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbf31c424a" id="BPMNShape_sbf31c424a">
        <omgdc:Bounds height="80.0" width="100.00000000000023" x="1004.9999999999998" y="1037.0370339845815"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbdc38081a" id="BPMNShape_sbdc38081a">
        <omgdc:Bounds height="80.0" width="100.00000000000011" x="129.99999724494103" y="1289.9999726613385"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s1ae13dea4" id="BPMNShape_s1ae13dea4">
        <omgdc:Bounds height="40.0" width="40.0" x="160.0" y="1440.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="se95f4dc58" id="BPMNShape_se95f4dc58">
        <omgdc:Bounds height="80.0" width="100.0" x="1004.9999787012753" y="1419.99993981256"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sb7c61e7f3" id="BPMNShape_sb7c61e7f3">
        <omgdc:Bounds height="28.0" width="28.0" x="1041.0" y="1586.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s6f5f0b670" id="BPMNShape_s6f5f0b670">
        <omgdc:Bounds height="80.0" width="100.0" x="130.0" y="1560.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s5c1d74106" id="BPMNShape_s5c1d74106">
        <omgdc:Bounds height="80.0" width="100.0" x="144.0" y="104.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s6ea338ef0" id="BPMNShape_s6ea338ef0">
        <omgdc:Bounds height="40.0" width="40.0" x="165.0" y="690.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s6bec7f205" id="BPMNShape_s6bec7f205">
        <omgdc:Bounds height="80.0" width="100.0" x="360.0" y="813.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s49f1b2dff" id="BPMNShape_s49f1b2dff">
        <omgdc:Bounds height="40.0" width="40.0" x="615.0" y="690.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbacaa6539" id="BPMNShape_sbacaa6539">
        <omgdc:Bounds height="80.0" width="100.0" x="130.0" y="900.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s8c5eda200" id="BPMNShape_s8c5eda200">
        <omgdc:Bounds height="40.0" width="40.0" x="160.0" y="1057.0370339845815"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s4a0c5a5b3" id="BPMNShape_s4a0c5a5b3">
        <omgdc:Bounds height="80.0" width="100.0" x="360.0" y="1131.4567035740993"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="s65ed85c32" id="BPMNEdge_s65ed85c32">
        <omgdi:waypoint x="197.19708638956803" y="717.7523097826088"/>
        <omgdi:waypoint x="360.0" y="821.2222222222223"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sb4177df32" id="BPMNEdge_sb4177df32">
        <omgdi:waypoint x="165.0" y="710.0"/>
        <omgdi:waypoint x="27.0" y="710.0"/>
        <omgdi:waypoint x="27.0" y="445.0"/>
        <omgdi:waypoint x="585.0" y="445.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sca4faaca9" id="BPMNEdge_sca4faaca9">
        <omgdi:waypoint x="180.0" y="1479.9428800856533"/>
        <omgdi:waypoint x="180.0" y="1560.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7f3117a31" id="BPMNEdge_s7f3117a31">
        <omgdi:waypoint x="622.7717391304348" y="717.7523097826088"/>
        <omgdi:waypoint x="459.9499999999999" y="821.2222222222222"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7a83e1182" id="BPMNEdge_s7a83e1182">
        <omgdi:waypoint x="684.9499999999803" y="144.0"/>
        <omgdi:waypoint x="779.9999999999362" y="144.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sabb0f80e1" id="BPMNEdge_sabb0f80e1">
        <omgdi:waypoint x="199.94886150228146" y="1459.9999986242872"/>
        <omgdi:waypoint x="1004.9999782183535" y="1459.9999432484028"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s342838adc" id="BPMNEdge_s342838adc">
        <omgdi:waypoint x="1055.0" y="1116.9870339845816"/>
        <omgdi:waypoint x="1055.0" y="1329.9999726613385"/>
        <omgdi:waypoint x="229.9499972447436" y="1329.9999726613385"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s6f91823ca" id="BPMNEdge_s6f91823ca">
        <omgdi:waypoint x="180.0" y="979.9499999999999"/>
        <omgdi:waypoint x="180.0" y="1057.0370339845815"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s6087dc86a" id="BPMNEdge_s6087dc86a">
        <omgdi:waypoint x="635.0" y="334.95000000000005"/>
        <omgdi:waypoint x="635.0" y="405.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sbca2fa5e8" id="BPMNEdge_sbca2fa5e8">
        <omgdi:waypoint x="243.94999999996867" y="144.0"/>
        <omgdi:waypoint x="359.99999999996294" y="144.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sc9ffd5f86" id="BPMNEdge_sc9ffd5f86">
        <omgdi:waypoint x="635.0" y="484.95000000000005"/>
        <omgdi:waypoint x="635.0" y="512.0"/>
        <omgdi:waypoint x="185.0" y="512.0"/>
        <omgdi:waypoint x="185.0" y="540.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sa91dc357f" id="BPMNEdge_sa91dc357f">
        <omgdi:waypoint x="229.94999999990745" y="1600.0"/>
        <omgdi:waypoint x="1041.0" y="1600.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s99f1ecf42" id="BPMNEdge_s99f1ecf42">
        <omgdi:waypoint x="459.94999999996566" y="144.0"/>
        <omgdi:waypoint x="585.0" y="144.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s96d6e74f0" id="BPMNEdge_s96d6e74f0">
        <omgdi:waypoint x="179.9999980915917" y="1369.9499726613385"/>
        <omgdi:waypoint x="179.99999957614477" y="1440.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s4af64b9ae" id="BPMNEdge_s4af64b9ae">
        <omgdi:waypoint x="194.88372435085776" y="29.949560210218113"/>
        <omgdi:waypoint x="194.30968992248063" y="104.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="seb4dab6dc" id="BPMNEdge_seb4dab6dc">
        <omgdi:waypoint x="459.9499999997666" y="580.0"/>
        <omgdi:waypoint x="585.0" y="580.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s77eaaf343" id="BPMNEdge_s77eaaf343">
        <omgdi:waypoint x="1054.999984779016" y="1499.94993981256"/>
        <omgdi:waypoint x="1054.9999978701285" y="1586.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s460023125" id="BPMNEdge_s460023125">
        <omgdi:waypoint x="410.0" y="892.9499999999999"/>
        <omgdi:waypoint x="410.0" y="1131.4567035740993"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s154f88cca" id="BPMNEdge_s154f88cca">
        <omgdi:waypoint x="197.644366197183" y="702.6760563380282"/>
        <omgdi:waypoint x="359.99999999999994" y="608.8599999999999"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sfdc2db890" id="BPMNEdge_sfdc2db890">
        <omgdi:waypoint x="199.9488601302705" y="1077.0370339845815"/>
        <omgdi:waypoint x="1004.9999999974438" y="1077.0370339845815"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s635d0f065" id="BPMNEdge_s635d0f065">
        <omgdi:waypoint x="459.95000000000005" y="1204.643893739175"/>
        <omgdi:waypoint x="1043.3303053381685" y="1592.2516084909817"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s0af075f45" id="BPMNEdge_s0af075f45">
        <omgdi:waypoint x="779.9999999999853" y="295.0"/>
        <omgdi:waypoint x="684.9499999999999" y="295.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s8cba1083e" id="BPMNEdge_s8cba1083e">
        <omgdi:waypoint x="185.0" y="619.9499999999999"/>
        <omgdi:waypoint x="185.0" y="690.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="seed403ef1" id="BPMNEdge_seed403ef1">
        <omgdi:waypoint x="654.9448872373141" y="710.0"/>
        <omgdi:waypoint x="780.0" y="710.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sed1d6ea77" id="BPMNEdge_sed1d6ea77">
        <omgdi:waypoint x="160.0" y="1077.0321300237972"/>
        <omgdi:waypoint x="29.0" y="1077.0"/>
        <omgdi:waypoint x="29.0" y="445.0"/>
        <omgdi:waypoint x="584.9999999998472" y="445.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7c3c16798" id="BPMNEdge_s7c3c16798">
        <omgdi:waypoint x="830.0" y="183.95"/>
        <omgdi:waypoint x="830.0" y="255.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s97c958549" id="BPMNEdge_s97c958549">
        <omgdi:waypoint x="622.3231896308819" y="702.6760563380282"/>
        <omgdi:waypoint x="459.95" y="608.86"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s9ca0cf513" id="BPMNEdge_s9ca0cf513">
        <omgdi:waypoint x="635.0" y="619.9499999999999"/>
        <omgdi:waypoint x="635.0" y="690.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s02982d5bc" id="BPMNEdge_s02982d5bc">
        <omgdi:waypoint x="1040.0" y="749.9499999999999"/>
        <omgdi:waypoint x="1040.0" y="940.0"/>
        <omgdi:waypoint x="229.95000000000002" y="940.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7290f05d1" id="BPMNEdge_s7290f05d1">
        <omgdi:waypoint x="879.9499999999999" y="710.0"/>
        <omgdi:waypoint x="990.0" y="710.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>