<?xml version='1.0' encoding='UTF-8'?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="rent001">
  <process id="rent_apply" name="公租房申请" isExecutable="true">
    <documentation>用于资格轮候公租房申请流程</documentation>
    <startEvent id="startEvent1" flowable:initiator="initiator"/>
    <userTask id="sb58cb8979" name="单位初审" flowable:candidateUsers="${hsQwApplyService.findUserByRoleAndOffice(officeCode,'DZ_JG')}"/>
    <userTask id="sca65c2c63" name="经办初审" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sbae041fee" name="处室领导初审" flowable:candidateGroups="HS_CSLD">
      <extensionElements>
        <modeler:group-info-name-HS_CSLD xmlns:modeler="http://flowable.org/modeler"><![CDATA[处室领导（住房保障处） (HS_CSLD)]]></modeler:group-info-name-HS_CSLD>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sfc1279869" name="发起配租复查" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s24f460c9d" name="配租复查确认" flowable:assignee="${initiator}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s42935f27b" name="单位配租复查审批" flowable:candidateUsers="${hsQwApplyService.findUserByRoleAndOffice(officeCode,'DZ_JG')}"/>
    <userTask id="seceb4a0b2" name="经办配租复查审批" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s79a7f1bfd" name="选择配租名单" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s85e7a7e91" name="配租公示方案审批"/>
    <userTask id="sbf31c424a" name="房源配租" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="sbdc38081a" name="申请人确认" flowable:assignee="${initiator}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="s1ae13dea4"/>
    <sequenceFlow id="s96d6e74f0" sourceRef="sbdc38081a" targetRef="s1ae13dea4"/>
    <userTask id="se95f4dc58" name="租赁合同上传" flowable:candidateGroups="FG_JG">
      <extensionElements>
        <modeler:group-info-name-FG_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（房管机构） (FG_JG)]]></modeler:group-info-name-FG_JG>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <endEvent id="sb7c61e7f3"/>
    <sequenceFlow id="seb4dab6dc" sourceRef="s42935f27b" targetRef="seceb4a0b2"/>
    <sequenceFlow id="sc9ffd5f86" sourceRef="sfc1279869" targetRef="s24f460c9d"/>
    <sequenceFlow id="s7a83e1182" sourceRef="sca65c2c63" targetRef="sbae041fee"/>
    <serviceTask id="s6f5f0b670" name="录入黑名单" flowable:delegateExpression="${hsQwApplyBpmServiceTask}"/>
    <userTask id="s5c1d74106" name="用户申请" flowable:assignee="${initiator}" flowable:skipExpression="${firstApply}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="s4af64b9ae" sourceRef="startEvent1" targetRef="s5c1d74106"/>
    <sequenceFlow id="sbca2fa5e8" sourceRef="s5c1d74106" targetRef="sb58cb8979"/>
    <sequenceFlow id="sca4faaca9" name="不承租" sourceRef="s1ae13dea4" targetRef="s6f5f0b670">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${applyAccepted!=0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sabb0f80e1" name="承租" sourceRef="s1ae13dea4" targetRef="se95f4dc58">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${applyAccepted==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sa91dc357f" sourceRef="s6f5f0b670" targetRef="sb7c61e7f3"/>
    <exclusiveGateway id="s6ea338ef0"/>
    <sequenceFlow id="s8cba1083e" sourceRef="s24f460c9d" targetRef="s6ea338ef0"/>
    <userTask id="s6bec7f205" name="不轮候申请" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="s49f1b2dff"/>
    <sequenceFlow id="s9ca0cf513" sourceRef="seceb4a0b2" targetRef="s49f1b2dff"/>
    <sequenceFlow id="s7290f05d1" sourceRef="s79a7f1bfd" targetRef="s85e7a7e91"/>
    <userTask id="sbacaa6539" name="线下配租确认" flowable:assignee="${initiator}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="s8c5eda200"/>
    <sequenceFlow id="s6f91823ca" sourceRef="sbacaa6539" targetRef="s8c5eda200"/>
    <sequenceFlow id="s02982d5bc" sourceRef="s85e7a7e91" targetRef="sbacaa6539"/>
    <sequenceFlow id="seed403ef1" name="通过" sourceRef="s49f1b2dff" targetRef="s79a7f1bfd">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckAudit==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s7f3117a31" name="资格不满足" sourceRef="s49f1b2dff" targetRef="s6bec7f205">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckAudit==2}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s97c958549" name="材料有问题" sourceRef="s49f1b2dff" targetRef="s42935f27b">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckAudit==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sfdc2db890" name="参与线下配租" sourceRef="s8c5eda200" targetRef="sbf31c424a">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${offlineRent==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sed1d6ea77" name="不参与线下配租" sourceRef="s8c5eda200" targetRef="sfc1279869">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${offlineRent==1}]]></conditionExpression>
    </sequenceFlow>
    <userTask id="s4a0c5a5b3" name="处室领导审核" flowable:candidateGroups="HS_CSLD">
      <extensionElements>
        <modeler:group-info-name-HS_CSLD xmlns:modeler="http://flowable.org/modeler"><![CDATA[处室领导（住房保障处） (HS_CSLD)]]></modeler:group-info-name-HS_CSLD>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="s460023125" sourceRef="s6bec7f205" targetRef="s4a0c5a5b3"/>
    <sequenceFlow id="s77eaaf343" sourceRef="se95f4dc58" targetRef="sb7c61e7f3"/>
    <sequenceFlow id="s342838adc" sourceRef="sbf31c424a" targetRef="sbdc38081a"/>
    <exclusiveGateway id="s80be2582f"/>
    <sequenceFlow id="sb4177df32" name="复查不参与，仅本次不参与" sourceRef="s6ea338ef0" targetRef="sfc1279869">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckStatus==1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s65ed85c32" name="确认不参与，资格已不满足" sourceRef="s6ea338ef0" targetRef="s6bec7f205">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckStatus==2}]]></conditionExpression>
    </sequenceFlow>
    <serviceTask id="sdee7c3bc9" name="流程取消" flowable:delegateExpression="${hsQwApplyBpmCancleTask}"/>
    <sequenceFlow id="s635d0f065" sourceRef="s4a0c5a5b3" targetRef="sdee7c3bc9"/>
    <sequenceFlow id="s57ac81146" sourceRef="sdee7c3bc9" targetRef="sb7c61e7f3"/>
    <sequenceFlow id="s7c3c16798" sourceRef="sbae041fee" targetRef="sfc1279869"/>
    <sequenceFlow id="se8e370bf0" name="是否政策性购房" sourceRef="sb58cb8979" targetRef="s80be2582f"/>
    <sequenceFlow id="s99f1ecf42" name="否" sourceRef="s80be2582f" targetRef="sca65c2c63">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${policyBuy==0 && compRecv == 0 && pubRent == 0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s35376d0fb" name="是" sourceRef="s80be2582f" targetRef="sdee7c3bc9">
      <documentation>不满足公租房申请条件</documentation>
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${policyBuy==1 || compRecv == 1 || pubRent == 1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s906774b84" sourceRef="s204cf5f19" targetRef="sfc1279869"/>
    <boundaryEvent id="s204cf5f19" attachedToRef="s24f460c9d" cancelActivity="true">
      <timerEventDefinition>
        <timeDuration>${timer}</timeDuration>
      </timerEventDefinition>
    </boundaryEvent>
    <sequenceFlow id="s154f88cca" name="确认参与" sourceRef="s6ea338ef0" targetRef="s42935f27b">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${recheckStatus==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s39582e147" sourceRef="s4be177037" targetRef="sfc1279869"/>
    <boundaryEvent id="s4be177037" attachedToRef="sbacaa6539" cancelActivity="true">
      <timerEventDefinition>
        <timeDuration>${timer}</timeDuration>
      </timerEventDefinition>
    </boundaryEvent>
    <boundaryEvent id="s7775a5958" attachedToRef="sbdc38081a" cancelActivity="true">
      <timerEventDefinition>
        <timeDuration>${timer}</timeDuration>
      </timerEventDefinition>
    </boundaryEvent>
    <sequenceFlow id="s4682271b5" sourceRef="s7775a5958" targetRef="s6f5f0b670"/>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_rent_apply">
    <bpmndi:BPMNPlane bpmnElement="rent_apply" id="BPMNPlane_rent_apply">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="180.0" y="0.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sb58cb8979" id="BPMNShape_sb58cb8979">
        <omgdc:Bounds height="80.0" width="100.0" x="365.27776219521127" y="104.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sca65c2c63" id="BPMNShape_sca65c2c63">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="104.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbae041fee" id="BPMNShape_sbae041fee">
        <omgdc:Bounds height="80.0" width="100.0" x="780.0" y="104.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sfc1279869" id="BPMNShape_sfc1279869">
        <omgdc:Bounds height="80.0" width="100.0" x="780.0" y="315.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s24f460c9d" id="BPMNShape_s24f460c9d">
        <omgdc:Bounds height="80.0" width="100.0" x="135.0" y="540.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s42935f27b" id="BPMNShape_s42935f27b">
        <omgdc:Bounds height="80.0" width="100.0" x="360.0" y="540.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="seceb4a0b2" id="BPMNShape_seceb4a0b2">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="540.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s79a7f1bfd" id="BPMNShape_s79a7f1bfd">
        <omgdc:Bounds height="80.0" width="100.0" x="780.0" y="670.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s85e7a7e91" id="BPMNShape_s85e7a7e91">
        <omgdc:Bounds height="80.0" width="100.0" x="990.0" y="670.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbf31c424a" id="BPMNShape_sbf31c424a">
        <omgdc:Bounds height="80.0" width="100.00000000000023" x="1004.9999999999998" y="1037.0370339845815"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbdc38081a" id="BPMNShape_sbdc38081a">
        <omgdc:Bounds height="80.0" width="100.00000000000011" x="129.99999724494103" y="1289.9999453226776"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s1ae13dea4" id="BPMNShape_s1ae13dea4">
        <omgdc:Bounds height="40.0" width="40.0" x="160.0" y="1440.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="se95f4dc58" id="BPMNShape_se95f4dc58">
        <omgdc:Bounds height="80.0" width="100.0" x="1004.9999787012753" y="1419.99993981256"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sb7c61e7f3" id="BPMNShape_sb7c61e7f3">
        <omgdc:Bounds height="28.0" width="28.0" x="1041.0" y="1586.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s6f5f0b670" id="BPMNShape_s6f5f0b670">
        <omgdc:Bounds height="80.0" width="100.0" x="130.0" y="1560.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s5c1d74106" id="BPMNShape_s5c1d74106">
        <omgdc:Bounds height="80.0" width="100.0" x="144.0" y="104.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s6ea338ef0" id="BPMNShape_s6ea338ef0">
        <omgdc:Bounds height="40.0" width="40.0" x="165.0" y="690.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s6bec7f205" id="BPMNShape_s6bec7f205">
        <omgdc:Bounds height="80.0" width="100.0" x="360.0" y="813.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s49f1b2dff" id="BPMNShape_s49f1b2dff">
        <omgdc:Bounds height="40.0" width="40.0" x="615.0" y="690.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbacaa6539" id="BPMNShape_sbacaa6539">
        <omgdc:Bounds height="80.0" width="100.0" x="130.0" y="900.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s8c5eda200" id="BPMNShape_s8c5eda200">
        <omgdc:Bounds height="40.0" width="40.0" x="160.0" y="1057.0370339845815"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s4a0c5a5b3" id="BPMNShape_s4a0c5a5b3">
        <omgdc:Bounds height="80.0" width="100.0" x="360.0" y="975.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s80be2582f" id="BPMNShape_s80be2582f">
        <omgdc:Bounds height="40.0" width="40.0" x="395.27776219521127" y="262.9999901877515"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sdee7c3bc9" id="BPMNShape_sdee7c3bc9">
        <omgdc:Bounds height="80.0" width="100.0" x="359.9999771118188" y="1154.9999755223612"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s204cf5f19" id="BPMNShape_s204cf5f19">
        <omgdc:Bounds height="31.0" width="31.000000000000014" x="118.93431766418858" y="556.3312285708611"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s4be177037" id="BPMNShape_s4be177037">
        <omgdc:Bounds height="31.0" width="31.0" x="113.92191441267707" y="919.9068896453092"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s7775a5958" id="BPMNShape_s7775a5958">
        <omgdc:Bounds height="31.0" width="31.0" x="114.23654727575757" y="1317.5726931279805"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="s65ed85c32" id="BPMNEdge_s65ed85c32">
        <omgdi:waypoint x="197.19708638956803" y="717.7523097826088"/>
        <omgdi:waypoint x="360.0" y="821.2222222222223"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sb4177df32" id="BPMNEdge_sb4177df32">
        <omgdi:waypoint x="165.0" y="710.0"/>
        <omgdi:waypoint x="27.0" y="710.0"/>
        <omgdi:waypoint x="27.0" y="355.0"/>
        <omgdi:waypoint x="779.9999999995844" y="355.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sca4faaca9" id="BPMNEdge_sca4faaca9">
        <omgdi:waypoint x="180.0" y="1479.9428800856533"/>
        <omgdi:waypoint x="180.0" y="1560.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7f3117a31" id="BPMNEdge_s7f3117a31">
        <omgdi:waypoint x="622.7717391304348" y="717.7523097826088"/>
        <omgdi:waypoint x="459.9499999999999" y="821.2222222222222"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s906774b84" id="BPMNEdge_s906774b84">
        <omgdi:waypoint x="134.9307940207362" y="556.3312285708611"/>
        <omgdi:waypoint x="134.88645530415357" y="355.0"/>
        <omgdi:waypoint x="780.0" y="355.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7a83e1182" id="BPMNEdge_s7a83e1182">
        <omgdi:waypoint x="684.9499999999803" y="144.0"/>
        <omgdi:waypoint x="779.9999999999362" y="144.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sabb0f80e1" id="BPMNEdge_sabb0f80e1">
        <omgdi:waypoint x="199.94886150228146" y="1459.9999986242872"/>
        <omgdi:waypoint x="1004.9999782183535" y="1459.9999432484028"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s342838adc" id="BPMNEdge_s342838adc">
        <omgdi:waypoint x="1055.0" y="1116.9870339845816"/>
        <omgdi:waypoint x="1055.0" y="1329.9999453226776"/>
        <omgdi:waypoint x="229.9499972447436" y="1329.9999453226776"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s6f91823ca" id="BPMNEdge_s6f91823ca">
        <omgdi:waypoint x="180.0" y="979.9499999999999"/>
        <omgdi:waypoint x="180.0" y="1057.0370339845815"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s39582e147" id="BPMNEdge_s39582e147">
        <omgdi:waypoint x="113.92419579483771" y="935.6192073082656"/>
        <omgdi:waypoint x="76.54066534720917" y="934.9469349362691"/>
        <omgdi:waypoint x="76.54066534720917" y="355.0"/>
        <omgdi:waypoint x="780.0" y="355.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sbca2fa5e8" id="BPMNEdge_sbca2fa5e8">
        <omgdi:waypoint x="243.95000000000002" y="144.0"/>
        <omgdi:waypoint x="365.27776219505546" y="144.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sc9ffd5f86" id="BPMNEdge_sc9ffd5f86">
        <omgdi:waypoint x="830.0" y="394.95000000000005"/>
        <omgdi:waypoint x="830.0" y="512.0"/>
        <omgdi:waypoint x="185.0" y="512.0"/>
        <omgdi:waypoint x="185.0" y="540.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sa91dc357f" id="BPMNEdge_sa91dc357f">
        <omgdi:waypoint x="229.94999999990745" y="1600.0"/>
        <omgdi:waypoint x="1041.0" y="1600.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s99f1ecf42" id="BPMNEdge_s99f1ecf42">
        <omgdi:waypoint x="433.3345813469652" y="284.2938177211882"/>
        <omgdi:waypoint x="585.0" y="144.87674369507326"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s96d6e74f0" id="BPMNEdge_s96d6e74f0">
        <omgdi:waypoint x="179.99999809159152" y="1369.9499453226776"/>
        <omgdi:waypoint x="179.99999957614477" y="1440.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s4af64b9ae" id="BPMNEdge_s4af64b9ae">
        <omgdi:waypoint x="194.88372435085776" y="29.949560210218113"/>
        <omgdi:waypoint x="194.30968992248063" y="104.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s57ac81146" id="BPMNEdge_s57ac81146">
        <omgdi:waypoint x="459.94997711181867" y="1226.36392979336"/>
        <omgdi:waypoint x="1043.1337529844745" y="1592.554268194219"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="se8e370bf0" id="BPMNEdge_se8e370bf0">
        <omgdi:waypoint x="415.27776219521127" y="183.95"/>
        <omgdi:waypoint x="415.27776219521127" y="262.9999901877515"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="seb4dab6dc" id="BPMNEdge_seb4dab6dc">
        <omgdi:waypoint x="459.9499999997666" y="580.0"/>
        <omgdi:waypoint x="585.0" y="580.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s77eaaf343" id="BPMNEdge_s77eaaf343">
        <omgdi:waypoint x="1054.999984779016" y="1499.94993981256"/>
        <omgdi:waypoint x="1054.9999978701285" y="1586.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s460023125" id="BPMNEdge_s460023125">
        <omgdi:waypoint x="410.0" y="892.9499999999999"/>
        <omgdi:waypoint x="410.0" y="975.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s154f88cca" id="BPMNEdge_s154f88cca">
        <omgdi:waypoint x="197.644366197183" y="702.6760563380282"/>
        <omgdi:waypoint x="359.99999999999994" y="608.8599999999999"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sfdc2db890" id="BPMNEdge_sfdc2db890">
        <omgdi:waypoint x="199.9488601302705" y="1077.0370339845815"/>
        <omgdi:waypoint x="1004.9999999974438" y="1077.0370339845815"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s635d0f065" id="BPMNEdge_s635d0f065">
        <omgdi:waypoint x="409.9999935068978" y="1054.95"/>
        <omgdi:waypoint x="409.99997726603" y="1154.9999755223612"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s8cba1083e" id="BPMNEdge_s8cba1083e">
        <omgdi:waypoint x="185.0" y="619.9499999999999"/>
        <omgdi:waypoint x="185.0" y="690.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="seed403ef1" id="BPMNEdge_seed403ef1">
        <omgdi:waypoint x="654.9448872373141" y="710.0"/>
        <omgdi:waypoint x="780.0" y="710.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s35376d0fb" id="BPMNEdge_s35376d0fb">
        <omgdi:waypoint x="395.77776219521127" y="283.4999901877515"/>
        <omgdi:waypoint x="24.233825645364902" y="283.4999901877515"/>
        <omgdi:waypoint x="24.233825645364902" y="1194.9999755223612"/>
        <omgdi:waypoint x="359.9999771118188" y="1194.9999755223612"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sed1d6ea77" id="BPMNEdge_sed1d6ea77">
        <omgdi:waypoint x="160.0" y="1077.0321300237972"/>
        <omgdi:waypoint x="29.0" y="1077.0"/>
        <omgdi:waypoint x="29.0" y="355.0"/>
        <omgdi:waypoint x="779.9999999996817" y="355.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s97c958549" id="BPMNEdge_s97c958549">
        <omgdi:waypoint x="622.3231896308819" y="702.6760563380282"/>
        <omgdi:waypoint x="459.95" y="608.86"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7c3c16798" id="BPMNEdge_s7c3c16798">
        <omgdi:waypoint x="830.0" y="183.95"/>
        <omgdi:waypoint x="830.0" y="315.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s4682271b5" id="BPMNEdge_s4682271b5">
        <omgdi:waypoint x="130.23654727575757" y="1349.522692517198"/>
        <omgdi:waypoint x="130.23654727575757" y="1512.2486214578012"/>
        <omgdi:waypoint x="157.31616138642806" y="1560.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s9ca0cf513" id="BPMNEdge_s9ca0cf513">
        <omgdi:waypoint x="635.0" y="619.9499999999999"/>
        <omgdi:waypoint x="635.0" y="690.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s02982d5bc" id="BPMNEdge_s02982d5bc">
        <omgdi:waypoint x="1040.0" y="749.9499999999999"/>
        <omgdi:waypoint x="1040.0" y="940.0"/>
        <omgdi:waypoint x="229.95000000000002" y="940.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7290f05d1" id="BPMNEdge_s7290f05d1">
        <omgdi:waypoint x="879.9499999999999" y="710.0"/>
        <omgdi:waypoint x="990.0" y="710.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>