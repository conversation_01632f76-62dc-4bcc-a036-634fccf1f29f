<?xml version="1.0" encoding="UTF-8"?>
<WorkBook xmlVersion="20211223" releaseVersion="11.0.0">
<TableDataMap>
<TableData name="ds1_item" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_item_code"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_item_level"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[apps]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT ROWNUM RN,
       ITEM_NAME,
       ITEM_LEVEL,
       ITEM_CODE,
       decode(cls2, '', cls, cls2) cls3
  FROM (SELECT DISTINCT I.ITEM_NAME,
                        I.ITEM_LEVEL,
                        I.ITEM_CODE,
                        --c.axis_name,
                        decode(c.axis_name, '', '', '0') cls2,
                        t.cls,
                        ''
          FROM CUX_IMA_REPORT_ITEM_LINE_SET@APPS_TO_GLKJ.HFZQ.COM A,
               CUX_IMA_REPORT_ITEM_CODE@APPS_TO_GLKJ.HFZQ.COM I,
               cux_rg_report_axis_contents c,
               (select *
                  from cux_hfgk_gl_fsg_tmp
                 where report_name = 'MA_利润明细表'
                 and version='${p_version}') t
         WHERE A.ITEM_CODE = I.ITEM_CODE
           and i.item_code = c.axis_name(+)
           and i.item_code = t.axis_name(+)

          AND A.DATA_VERSION='${p_version}' --ADD BY LSX 20230302
           AND I.DATA_VERSION='${p_version}' --ADD BY LSX 20230302 
              
           AND A.FLAG = 'Y'
           AND I.SHOW_RULES2 = 'Y'
        
        ${if(len(p_item_code)=0, "", " AND I.ITEM_CODE IN ('"+p_item_code+"') ")}
        ${if(len(p_item_level)=0, "", " and i.item_level in ("+p_item_level+") " )}
        
         ORDER BY TO_NUMBER(I.ITEM_CODE))]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O>
<![CDATA[CNY]]></O>
</Parameter>
<Parameter>
<Attributes name="p_ledger"/>
<O>
<![CDATA[2021]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O>
<![CDATA[2025-01]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O>
<![CDATA[1210]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O>
<![CDATA[2025-01]]></O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O>
<![CDATA[V2025-001]]></O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O>
<![CDATA[10]]></O>
</Parameter>
<Parameter>
<Attributes name="p_item_level"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with base as (SELECT ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       L.LINE_CODE||'-'||L.LINE_NAME LINE_NAME,
       
       AXIS_NAME,
       
       sum(decode(data_source, 'MA', 1, DECODE(R.CHANGE_SIGN, 'Y', -1, 1)) * nvl(PTD_BAL, 0)) PTD_BAL ,
       sum(decode(data_source, 'MA', 1, DECODE(R.CHANGE_SIGN, 'Y', -1, 1)) * nvl(END_BAL, 0)) END_BAL
  FROM (SELECT COM3.COM1_CODE COM1_CODE,
               COM3.COM1_CODE || '-' || COM3.COM1_NAME COM1_NAME,
               COM3.COM2_CODE COM2_CODE,
               COM3.COM2_CODE || '-' || COM3.COM2_NAME COM2_NAME,
               COM3.COM3_CODE COM3_CODE,
               COM3.COM3_CODE || '-' || COM3.COM3_NAME COM3_NAME,
               COM3.DEPT_CODE,

               d.data_source,
               case when D.period_name>='2024-10' AND D.LINE_CODE='1060' then '2070' else D.LINE_CODE end LINE_CODE,    -- update by csq 2024.10.24 资管条线映射到资管子公司           
               D.AXIS_NAME,
               
               SUM(NVL(D.PTD_BAL, 0)) PTD_BAL,               
               sum(case
                 when d.period_name = '${p_period_to}' then
                  NVL(D.END_BAL, 0)
               end) END_BAL
               
          FROM hfglkj.CUX_GL_FSG_DATA3 D
          left join  hfglkj.CUX_YYWD_LEDGER_V  COM3
          on D.SEGMENT1 = COM3.COM3_CODE
          where (('10' in ('${p_dept_type}') and COM3.DEPT_CODE = '0' AND
               D.SEGMENT2 <> '41126') OR
               ('20' in ('${p_dept_type}') and COM3.DEPT_CODE = '41126' AND
               D.SEGMENT2 = '41126') OR
               ('30' in ('${p_dept_type}') and COM3.DEPT_CODE = 'T'))
              
           AND D.LEDGER_ID IN ('${p_ledger}')

	        --add by lsx 20220222
	         and substr('${p_period}', 1, 4) between
	             com3.effective_start_year and
	             nvl(com3.effective_end_year,
	                 substr('${p_period}', 1, 4))

           --------add by lsx 20230525-------------
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year1 and
                       nvl(com3.effective_end_year1,
                           substr('${p_period}', 1, 4))
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year2 and
                       nvl(com3.effective_end_year2,
                           substr('${p_period}', 1, 4))
                    ------add by lsx 20230525 end ---------------
                    
          --add by lsx 20230310
          ${if(len(p_source)=0, "", " and d.is_restore_data in ('"+p_source+"') ")}
          
           AND D.PERIOD_NAME between '${p_period}' and '${p_period_to}'
           AND D.CURRENCY_CODE = '${p_currency}'
           AND D.ACTUAL_FLAG = 'A'
           and d.data_version='${p_version}'

           AND COM3.COM1_CODE NOT IN ('1110', '6100', '7100', '8100', '9901')
           
         GROUP BY COM3.COM1_CODE,
                  COM3.COM1_NAME,
                  COM3.COM2_CODE,
                  COM3.COM2_NAME,
                  COM3.COM3_CODE,
                  COM3.COM3_NAME,
                  COM3.DEPT_CODE,

                  d.data_source,
                  case when D.period_name>='2024-10' AND D.LINE_CODE='1060' then '2070' else D.LINE_CODE end,
                  D.AXIS_NAME
		   union all 
           SELECT COM3.COM1_CODE COM1_CODE,
               COM3.COM1_CODE || '-' || COM3.COM1_NAME COM1_NAME,
               COM3.COM2_CODE COM2_CODE,
               COM3.COM2_CODE || '-' || COM3.COM2_NAME COM2_NAME,
               COM3.COM3_CODE COM3_CODE,
               COM3.COM3_CODE || '-' || COM3.COM3_NAME COM3_NAME,
               COM3.DEPT_CODE,

               d.data_source,
               case when D.period_name>='2024-10' AND D.LINE_CODE='1060' then '2070' else D.LINE_CODE end LINE_CODE,    
               '100611' AXIS_NAME,
               
               SUM(NVL(D.PTD_BAL, 0)) PTD_BAL,               
               sum(case
                 when d.period_name = '${p_period_to}' then
                  NVL(D.END_BAL, 0)
               end) END_BAL
               
          FROM hfglkj.CUX_GL_FSG_DATA3 D
          left join  hfglkj.CUX_YYWD_LEDGER_V  COM3
          on D.SEGMENT1 = COM3.COM3_CODE
          where (('10' in ('${p_dept_type}') and COM3.DEPT_CODE = '0' AND
               D.SEGMENT2 <> '41126') OR
               ('20' in ('${p_dept_type}') and COM3.DEPT_CODE = '41126' AND
               D.SEGMENT2 = '41126') OR
               ('30' in ('${p_dept_type}') and COM3.DEPT_CODE = 'T'))
              
           AND D.LEDGER_ID IN ('${p_ledger}')

	         and substr('${p_period}', 1, 4) between
	             com3.effective_start_year and
	             nvl(com3.effective_end_year,
	                 substr('${p_period}', 1, 4))
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year1 and
                       nvl(com3.effective_end_year1,
                           substr('${p_period}', 1, 4))
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year2 and
                       nvl(com3.effective_end_year2,
                           substr('${p_period}', 1, 4))
          ${if(len(p_source)=0, "", " and d.is_restore_data in ('"+p_source+"') ")}
          
           AND D.PERIOD_NAME between '${p_period}' and '${p_period_to}'
           AND D.CURRENCY_CODE = '${p_currency}'
           AND D.ACTUAL_FLAG = 'A'
           and d.data_version='${p_version}'
           and d.segment6 = '311116'
           and d.AXIS_NAME = '100610'
           AND COM3.COM1_CODE NOT IN ('1110', '6100', '7100', '8100', '9901')
           
         GROUP BY COM3.COM1_CODE,
                  COM3.COM1_NAME,
                  COM3.COM2_CODE,
                  COM3.COM2_NAME,
                  COM3.COM3_CODE,
                  COM3.COM3_NAME,
                  COM3.DEPT_CODE,
                  d.data_source,
                  case when D.period_name>='2024-10' AND D.LINE_CODE='1060' then '2070' else D.LINE_CODE end) T
       left join hfglkj.CUX_IMA_LINE_BASIC_CODE L
	   on T.LINE_CODE = L.LINE_CODE
       left join hfglkj.CUX_IMA_REPORT_ITEM_CODE R    
       on T.AXIS_NAME = R.ITEM_CODE
        where r.SHOW_RULES2 = 'Y'
   and R.data_version = '${p_version}' --ADD BY LSX 20230302
   ${if(len(p_item_level)=0, "", " and r.item_level in ("+p_item_level+") ")}
   
   ---${if(len(p_item_code)=0, "", " AND R.ITEM_CODE IN ('"+p_item_code+"') ")}  
   
   ${if(len(p_com1)=0, "", " AND T.COM1_CODE IN ('"+p_com1+"') ")}  
   ${if(len(p_com2)=0, "", " AND T.COM2_CODE IN ('"+p_com2+"') ")}    
   ${if(len(p_com3)=0, "", " AND T.COM3_CODE IN ('"+p_com3+"') ")} 

group by 
       ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       L.LINE_CODE||'-'||L.LINE_NAME,
       AXIS_NAME
 
),
base1 as
(SELECT ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       LINE_NAME,
       '102000' AXIS_NAME,
	   ptd_bal*0.25 as ptd_bal,
	   end_bal*0.25 as end_bal
          from base l
		  where line_code in('2050','2060')
		  and axis_name='101990'
		  union all
		  SELECT ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME,
       '102010' AXIS_NAME,
	   sum(ptd_bal) ptd_bal,
	   sum(end_bal) end_bal
	   from
	(SELECT ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       LINE_NAME,
       '102000' AXIS_NAME,
	   ptd_bal,
	   end_bal
          from base l
		  where line_code in('2050','2060')
		  and axis_name='101990'
        union all		  
      SELECT ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       L.LINE_CODE,
       L.LINE_NAME,
       '102000' AXIS_NAME,
	   -ptd_bal*0.25 as ptd_bal,
	   -end_bal*0.25 as end_bal
          from base l
		  where line_code in('2050','2060')
		  and axis_name='101990'
		  )
		  where 1=1	  
		  group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME 
		  ),

base2 as
(

select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME,
       AXIS_NAME,
	   sum(ptd_bal) ptd_bal,
	   sum(end_bal) end_bal
from(
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       LINE_NAME,
       AXIS_NAME,
	   ptd_bal,
	   end_bal
from base1 l
union all
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       LINE_NAME,
       AXIS_NAME,
	   ptd_bal,
	   end_bal
from base l
where line_code in('2050','2060')
and axis_name <>'102000'
and axis_name <>'102010'
union all
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       LINE_NAME,
       AXIS_NAME,
	   ptd_bal,
	   end_bal
from base l
where line_code <>'2050'
and line_code <>'2060'
-- 固定费用不包含客户维护费
union all 
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       LINE_NAME,
       '101230' AXIS_NAME,
	   -ptd_bal,
	   -end_bal
from base l
where ${if(p_version='V2025-001', "1=1", "1=0")}
  and axis_name ='101920'
 union all 
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       LINE_NAME,
       '100612' AXIS_NAME,
	   ptd_bal,
	   end_bal
from base l
where ${if(p_version='V2025-001', "1=1", "1=0")}
  and axis_name ='100610'      
 union all 
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       LINE_NAME,
       '100612' AXIS_NAME,
	   -ptd_bal,
	   -end_bal
from base l
where ${if(p_version='V2025-001', "1=1", "1=0")}
  and axis_name ='100611' ) t1
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
         ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
         ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
         LINE_CODE,
         LINE_NAME,
         AXIS_NAME

),
-- 25版KPI利润（考核利润）根据所得税费用调整(一级分公司)
base_2025 as(

select  ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
         ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
         ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       LINE_CODE,
       line_name,
       axis_name,
       sum(ptd_bal) ptd_bal,
       sum(end_bal) end_bal
from
(select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       LINE_CODE,
       line_name,
       '102030' axis_name,
       sum(nvl(ptd_bal,0)) ptd_bal,
       sum(nvl(end_bal,0)) end_bal
       from base2
       where 1=1 
       ${if(p_level='1', "and axis_name='102010' ", " and 1=0 ")}
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       LINE_CODE,
       LINE_NAME,
       AXIS_NAME
union all 
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       LINE_CODE,
       line_name,
       '102030' axis_name,
       case when sum(nvl(ptd_bal,0))>0 then 0 else sum(nvl(ptd_bal,0)) end ptd_bal,
       case when sum(nvl(end_bal,0))>0 then 0 else sum(nvl(end_bal,0)) end end_bal
       from base2
       where 1=1 
        ${if(p_level='1', "and axis_name='102000' ", " and 1=0 ")}
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       LINE_CODE,
       LINE_NAME,
       AXIS_NAME
) t
where line_code <>'1070'
  and line_code <>'1110'
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       LINE_CODE,
       LINE_NAME,
       AXIS_NAME
),
base_2025_sum as(
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       '9999' LINE_CODE,
       '9999-汇总' line_name,
       '102030' axis_name,
       sum(ptd_bal) ptd_bal,
       sum(end_bal) end_bal
from(
    select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
           ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
           ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
    '102030' axis_name,
     sum(ptd_bal) ptd_bal,
     sum(end_bal) end_bal
 from base2
where 1=1 
      ${if(p_level='1', "and axis_name='102010' ", " and 1=0 ")}
      and line_code <> '1070'
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
         ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
         ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       AXIS_NAME
union all 
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       '102030' axis_name,
       case when sum(ptd_bal)>0 then 0 else  sum(ptd_bal) end ptd_bal,
       case when sum(end_bal)>0 then 0 else  sum(end_bal) end end_bal
 from base2
where 1=1 
      ${if(p_level='1', "and axis_name='102000' ", " and 1=0 ")}
      and line_code <> '1070'
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       AXIS_NAME
) t
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       AXIS_NAME

),
base_2025_fgsgg as(
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       '1110' line_code,
       '1110-分公司公共' line_name,
       '102030' axis_name,
        sum(ptd_bal) ptd_bal,
        sum(end_bal) end_bal   
from(
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
  axis_name,
  ptd_bal,
  end_bal
 from base_2025_sum
where axis_name='102030'
union all
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       axis_name,
       -sum(ptd_bal) ptd_bal,
       -sum(end_bal) end_bal
 from base_2025
where axis_name='102030'
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       AXIS_NAME
) t
group by ${if(p_level='1', "COM1_CODE, COM1_NAME ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE", "")}
)
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
               ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
               ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
        line_code,
        line_name,
        axis_name,

        round(ptd_bal, 2) ptd_bal,
        round(end_bal, 2) end_bal        
        
  from  (select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME,
       AXIS_NAME,
	   ptd_bal,
       end_bal	     
          from base2
         where 1 = 1
          ${if(p_version<>'V2023-001'&&p_version<>'V2022-001'&&p_version<>'V2021-001', "and line_code<>'1070'", "")}
          ${if(p_version='V2025-001', "AND axis_name <> '102030'", "")}
        ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
		${if(len(p_item_code)=0, "", " AND axis_name IN ('"+p_item_code+"') ")}  
        
        union all
        select ${if(p_level='1', "t1.COM1_CODE, t1.COM1_NAME, ", "")}
        ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
        ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
        LINE_CODE,
        LINE_NAME,
        AXIS_NAME,
	    ptd_bal,
        end_bal	     
          from base_2025 t1
         where 1=1
        ${if(p_version<>'V2023-001'&&p_version<>'V2022-001'&&p_version<>'V2021-001', "and line_code<>'1070'", "")}
        ${if(p_version='V2025-001', "", "and 1=0")}
        ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
		${if(len(p_item_code)=0, "", " AND axis_name IN ('"+p_item_code+"') ")} 

       union all 
       select ${if(p_level='1', "t1.COM1_CODE, t1.COM1_NAME, ", "")}
              ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
              ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}     
        LINE_CODE,
        LINE_NAME,
        AXIS_NAME,
	    ptd_bal,
        end_bal	 
       from base_2025_fgsgg t1
        where 1=1
       ${if(p_version='V2025-001', "", "and 1=0")}
       ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
	   ${if(len(p_item_code)=0, "", " AND axis_name IN ('"+p_item_code+"') ")} 
        union all
        select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
               ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
               ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
        
               '9999' LINE_CODE,
               '9999-汇总' line_name,
               axis_name,
              sum(ptd_bal) ptd_bal,
               sum(end_bal) end_bal
          from base2
         where 1=1
           ${if(p_version<>'V2023-001'&&p_version<>'V2022-001'&&p_version<>'V2021-001', "and line_code<>'1070'", "")}
           and  '9999' in ('${p_line_code}' )
           ${if(p_version='V2025-001', "and axis_name<>'102030'", "")}
		   ${if(len(p_item_code)=0, "", " AND axis_name IN ('"+p_item_code+"') ")} 
         group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
                  ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
                  ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
                   axis_name
         union all 
         select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
                ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
               ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
                LINE_CODE,
                line_name,
                axis_name,
                ptd_bal,
                end_bal
         from base_2025_sum
         where 1=1
          ${if(p_version='V2025-001', "", " and 1=0")}
          ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
		  ${if(len(p_item_code)=0, "", " AND axis_name IN ('"+p_item_code+"') ")} 
          
                   ) t

ORDER BY decode(LINE_CODE,'1010',1,'1020',2,'2080',3,'1030',4,'1040',5,'1045',6,'1050',7,'1060',8,'1070',9,'2050',10,'2060',11,'1080',12,'1090',13,'1110',14,'1190',15,'2010',16,'2020',17,'2030',18,'2040',19,'2070',20), 
${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
AXIS_NAME]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_ledger" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_role"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[apps]]></DatabaseName>
</Connection>
<Query>
<![CDATA[--账套：
select *
  from (SELECT l.LEDGER_ID,
               l.LEDGER_ID || ' ' || l.NAME || ' ' || l.DESCRIPTION ledger_name
          FROM GL_LEDGERS l
         where (l.NAME like 'A%' or l.NAME like 'O%' or l.NAME like 'D%')
           and l.LEDGER_ID not in (2184, 2061)
        union all
        select to_number(i.code) ledger_id,
               i.code || ' ' || i.name || ' ' || i.remark ledger_name
          from cux_ima_dim_info@apps_to_glkj.hfzq.com i
         where i.type_code = '140') l
 where 1 = 1
   and exists
 (select *
          from (select distinct r.ledger_id
                  from cux_ima_role_maintain@apps_to_glkj.hfzq.com r
                 where r.role_name in
                       (select regexp_substr('${fine_role}',
                                             '[^,]A+',
                                             1,
                                             level,
                                             'i')
                          from dual
                        connect by level <=
                                   length('${fine_role}') -
                                   length(regexp_replace('${fine_role}',
                                                         ',',
                                                         '')) + 1)) t
         where l.ledger_id =
               decode(t.ledger_id, 'T', l.ledger_id, t.ledger_id)
        
        )

 order by LEDGER_ID, ledger_NAME
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_line_code" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_role"/>
<O>
<![CDATA[管会-计财总部]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[30421]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with old as (
select * from
(SELECT LINE_CODE, LINE_NAME, LINE_CODE || ' ' || LINE_NAME LINE_NAME2
  FROM CUX_IMA_LINE_BASIC_CODE l
 where 1 = 1
   and exists
 (select *
          from (select distinct r.line_code
                  from cux_ima_role_maintain r
                 where r.role_name in
                       (select regexp_substr('${fine_role}',
                                             '[^,]A+',
                                             1,
                                             level,
                                             'i')
                          from dual
                        connect by level <=
                                   length('${fine_role}') -
                                   length(regexp_replace('${fine_role}',
                                                         ',',
                                                         '')) + 1)) t
         where l.line_code =
               decode(t.line_code, 'T', l.line_code, t.line_code)
        
        )
union all
SELECT '9999' LINE_CODE, '汇总' LINE_NAME, '9999-汇总' LINE_NAME2
  FROM dual
 ORDER BY LINE_CODE
 )
 ORDER BY decode(LINE_CODE,'1010',1,'1020',2,'2080',3,'1030',4,'1040',5,'1045',6,'1050',7,'1060',8,'1070',9,'2050',10,'2060',11,'1175',12,'1080',13,'1090',14,'1110',15,'1190',16,'2010',17,'2020',18,'2030',19,'2040',20,'2070',21)
 ),
person_auth as (
select case when substr(auth_code, 1, 1)  = '1' then '1010' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --经纪业务
select case when substr(auth_code, 2, 1)  = '1' then '1020' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --信用业务
select case when substr(auth_code, 3, 1) = '1' and substr(auth_code, 4, 1) = '1' and substr(auth_code, 5, 1) = '1' then '2080' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all -- 投资管理总部 
select case when substr(auth_code, 3, 1)  = '1' then '1030' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --权益业务
select case when substr(auth_code, 4, 1)  = '1' then '1040' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --固收业务
select case when substr(auth_code, 5, 1)  = '1' then '1045' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --场外衍生品部
select case when substr(auth_code, 6, 1)  = '1' then '1060' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --资管业务
select case when substr(auth_code, 7, 1)  = '1' then '2050' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --股权业务部
select case when substr(auth_code, 8, 1)  = '1' then '2060' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --债券业务部
select case when substr(auth_code, 9, 1)  = '1' then '1080' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --托管业务
select case when substr(auth_code, 10, 1) = '1' then '1090' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --研究所
select case when substr(auth_code, 11, 1) = '1' then '1110' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --分公司公共
select case when substr(auth_code, 12, 1) = '1' then '1190' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --总部公共
select case when substr(auth_code, 13, 1) = '1' then '2010' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --兴银基金
select case when substr(auth_code, 14, 1) = '1' then '2020' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --华福投资
select case when substr(auth_code, 15, 1) = '1' then '2030' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --华福资本
select case when substr(auth_code, 16, 1) = '1' then '2040' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --华福国际
select case when substr(auth_code, 17, 1) = '1' then '2070' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --华福资管子公司
select case when substr(auth_code, 19, 1) = '1' then '1070' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --投行业务
select case when substr(auth_code, 20, 1) = '1' then '1050' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username} union all  --资金中心
select case when substr(auth_code, 21, 1) = '1' then '1175' else '' end as line_code from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = ${fine_username}            --销售交易部
),
result as (
	select old.line_code, old.line_name, old.line_name2
	from old join person_auth
	on old.line_code = person_auth.line_code
	union all
	select '9999', '汇总', '9999-汇总' from dual
)
select * from result]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_period" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hrs]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT P.PERIOD_NAME 
FROM HRS_CORE_FIN_PERIOD P 
WHERE PERIOD_STATUS IN ('O','C','F')
and period_name not like '%-13'  --add by lsx 20230302
ORDER BY P.PERIOD_NAME DESC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_type" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select '10' type1, '本期' name1 from dual
union all
select '20' type1, '本年累计' name1 from dual]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_level" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select '1' type1, '一级' name1 from dual
union all
select '2' type1, '二级' name1 from dual
union all
select '3' type1, '三级' name1 from dual]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_com3" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_com1"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_role"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[apps]]></DatabaseName>
</Connection>
<Query>
<![CDATA[
select distinct c.com_code,
                c.com_name,
                c.com_code || ' ' || c.com_name com_name2
  from (select v.FLEX_VALUE,
               v.description,
               v.FLEX_VALUE || ' ' || v.description description2
          from fnd_flex_value_sets s, fnd_flex_values_vl v
         where 1 = 1
           and s.FLEX_VALUE_SET_NAME = 'HF_COA_CO'
           and s.FLEX_VALUE_SET_ID = v.FLEX_VALUE_SET_ID
           and v.SUMMARY_FLAG = 'N'
           and (v.FLEX_VALUE not liKE 'B%' and v.FLEX_VALUE not liKE 'T%')
        
        union all
        SELECT I.CODE FLEX_VALUE, I.NAME, I.CODE || I.NAME DESCRIPTION2
          FROM CUX_IMA_DIM_INFO@APPS_TO_GLKJ.HFZQ.COM I
         WHERE I.TYPE_CODE = '150') t,
       cux_yywd_ledger@apps_to_glkj.hfzq.com c
 where 1 = 1
   and t.FLEX_VALUE = c.com_code
   AND COM_LEVEL1 NOT IN ('1110', '6100', '7100', '8100', '9901')
 ${if(len(p_com1) = 0, "", " and com_level1 in ('" + p_com1 + "') ") }
 ${if(len(p_com2) = 0, "", " and com_level2 in ('" + p_com2 + "') ") }
      
   and substr('${p_period}', 1, 4) between c.effective_start_year and
       nvl(c.effective_end_year, substr('${p_period}', 1, 4))
      
   and exists
 (select *
          from (select distinct r.com1_code,
                                r.com2_code,
                                r.com3_code,
                                r.dept_code
                  from cux_ima_role_maintain@apps_to_glkj.hfzq.com r
                 where r.role_name in
                       (select regexp_substr('${fine_role}',
                                             '[^,]A+',
                                             1,
                                             level,
                                             'i')
                          from dual
                        connect by level <=
                                   length('${fine_role}') -
                                   length(regexp_replace('${fine_role}',
                                                         ',',
                                                         '')) + 1)) t
         where c.com_level1 =
               decode(t.com1_code, 'T', c.com_level1, t.com1_code)
           and c.com_level2 =
               decode(nvl(t.com2_code, 'T'), 'T', c.com_level2, t.com2_code)
              
           and c.com_code =
               decode(nvl(t.com3_code, 'T'), 'T', c.com_code, t.com3_code)
           and c.dept_code =
               decode(nvl(t.dept_code, 'ALL'), 'ALL', c.dept_code, t.dept_code)
        
        )

 order by c.com_code
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_com1" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="fine_username"/>
<O>
<![CDATA[2522]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O>
<![CDATA[2025-01]]></O>
</Parameter>
<Parameter>
<Attributes name="b1"/>
<O>
<![CDATA[总部]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select * from
(select  com_code, com_name, com_code || ' ' || com_name com_name2,
 decode((select staff_id from hfglkj.tbb_glkj_person_auth where p_type='分支机构考核利润' and staff_id = '${fine_username}'), null, 0, 1) as para_auth
  from cux_yywd_com c
 where com_level = 1
   AND c.COM_CODE NOT IN ('1110', '6100', '7100', '8100', '9901','6511')
    	        --------add by lsx 20230525-------------
   and substr('${p_period}', 1, 4) between c.effective_start_year and
	       nvl(c.effective_end_year, substr('${p_period}', 1, 4))  
   ) t
 ${IF(FIND("总部", b1)=0, "where para_auth=1 or com_code in ('9999'", "")}
   	-- 9999为不存在的一个code，占位码
   	${IF(FIND("福州分公司", b1)!=0, ",1210", "")}
   	${IF(FIND("莆田分公司", b1)!=0, ",1310", "")}
   	${IF(FIND("泉州分公司", b1)!=0, ",1410", "")}
   	${IF(FIND("厦门分公司", b1)!=0, ",1510", "")}
   	${IF(FIND("漳州分公司", b1)!=0, ",1610", "")}
   	${IF(FIND("龙岩分公司", b1)!=0, ",1710", "")}
   	${IF(FIND("三明分公司", b1)!=0, ",1810", "")}
   	${IF(FIND("南平分公司", b1)!=0, ",1910", "")}
   	${IF(FIND("宁德分公司", b1)!=0, ",2010", "")}
   	${IF(FIND("上海分公司", b1)!=0, ",2110", "")}
   	${IF(FIND("华南分公司", b1)!=0, ",2210", "")}
   	${IF(FIND("广东分公司", b1)!=0, ",2211", "")}
   	${IF(FIND("北京分公司", b1)!=0, ",2310", "")}
   	${IF(FIND("河北分公司", b1)!=0, ",2322", "")}
   	${IF(FIND("浙江分公司", b1)!=0, ",2410", "")}
   	${IF(FIND("宁波分公司", b1)!=0, ",2432", "")}
   	${IF(FIND("东北分公司", b1)!=0, ",2510", "")}
   	${IF(FIND("西南分公司", b1)!=0, ",2610", "")}
   	${IF(FIND("重庆分公司", b1)!=0, ",2615", "")}
   	${IF(FIND("西北分公司", b1)!=0, ",2710", "")}
   	${IF(FIND("新疆分公司", b1)!=0, ",2717", "")}
   	--${IF(FIND("华中分公司", b1)!=0, ",2810", "")}
   	${IF(FIND("湖北分公司", b1)!=0, ",2810", "")}
   	${IF(FIND("湖南分公司", b1)!=0, ",2816", "")}
   	${IF(FIND("河南分公司", b1)!=0, ",2817", "")}
   	${IF(FIND("山东分公司", b1)!=0, ",2910", "")}
   	${IF(FIND("江苏分公司", b1)!=0, ",3010", "")}
   	${IF(FIND("苏州分公司", b1)!=0, ",3011", "")}
${IF(FIND("总部", b1)=0, ")", "")}
   order by decode(com_name,'福州分公司',1,'宁德分公司',2,'南平分公司',3,'泉州分公司',4,'莆田分公司',5,'三明分公司',6,'厦门分公司',7,'漳州分公司',8,'龙岩分公司',9,'浙江分公司',10,'上海分公司',11,'江苏分公司',12,'苏州分公司',13,'北京分公司',14,'华南分公司',15,'华中分公司',16,'湖北分公司',16,'西南分公司',17,'山东分公司',18,'西北分公司',19,'东北分公司',20)]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_com2" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_com1"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="fine_role"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select com_code, com_name, com_code || ' ' || com_name com_name2
  from cux_yywd_com c
 where com_level = 2
 ${if(len(p_com1) = 0, "", " and com_level1 in ('" + p_com1 + "') ") }
   AND COM_LEVEL1 NOT IN ('1110', '6100', '7100', '8100', '9901')
         --------add by lsx 20230525-------------
   and substr('${p_period}', 1, 4) between c.effective_start_year and
       nvl(c.effective_end_year, substr('${p_period}', 1, 4))  
      ------add by lsx 20230525 end ---------------
      
   and exists
 (select *
          from (select distinct r.com1_code,
                                r.com2_code,
                                r.com3_code,
                                r.dept_code
                  from cux_ima_role_maintain r
                 where r.role_name in
                       (select regexp_substr('${fine_role}',
                                             '[^,]A+',
                                             1,
                                             level,
                                             'i')
                          from dual
                        connect by level <=
                                   length('${fine_role}') -
                                   length(regexp_replace('${fine_role}',
                                                         ',',
                                                         '')) + 1)) t
         where c.com_level1 =
               decode(t.com1_code, 'T', c.com_level1, t.com1_code)
           and c.com_code =
               decode(nvl(t.com2_code, 'T'), 'T', c.com_code, t.com2_code)
        
        )

 order by com_code]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_period_to" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_period"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[hrs]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT P.PERIOD_NAME 
FROM HRS_CORE_FIN_PERIOD P 
WHERE PERIOD_STATUS IN ('O','C','F')
and P.PERIOD_NAME >= '${p_period}'
and p.period_year=substr('${p_period}', 1, 4)
and period_name not like '%-13'  --add by lsx 20230302
ORDER BY P.PERIOD_NAME DESC]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_item_code" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_version"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_item_level"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT item_code, item_code || ' ' || trim(item_name) item_name
  FROM cux_ima_report_item_code
 WHERE 1 = 1
  AND SHOW_RULES2 = 'Y'
  and data_version='${p_version}' --ADD BY LSX 20230302
   ${if(len(p_item_level)=0, "", " and item_level in ("+p_item_level+") " )}
 order by serial_num]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_version" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[select i.code
  from cux_ima_dim_info i
 where i.type_code = '130'
 order by i.code
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_dept_type" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters/>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[--类型 参数 下拉框
SELECT  '10' TYPE1, '本部' NAME1 FROM DUAL
 UNION ALL
 SELECT  '20' TYPE1, '直属' NAME1 FROM DUAL
 UNION ALL
 SELECT  '30' TYPE1, '汇总' NAME1 FROM DUAL]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds1_bak" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_line_code"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_ledger"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[apps]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with base as (SELECT ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       L.LINE_CODE||'-'||L.LINE_NAME LINE_NAME,
       
       AXIS_NAME,
       
       sum(decode(data_source, 'MA', 1, DECODE(R.CHANGE_SIGN, 'Y', -1, 1)) * nvl(PTD_BAL, 0)) PTD_BAL ,
       sum(decode(data_source, 'MA', 1, DECODE(R.CHANGE_SIGN, 'Y', -1, 1)) * nvl(END_BAL, 0)) END_BAL
  FROM (SELECT COM3.COM1_CODE COM1_CODE,
               COM3.COM1_CODE || '-' || COM3.COM1_NAME COM1_NAME,
               COM3.COM2_CODE COM2_CODE,
               COM3.COM2_CODE || '-' || COM3.COM2_NAME COM2_NAME,
               COM3.COM3_CODE COM3_CODE,
               COM3.COM3_CODE || '-' || COM3.COM3_NAME COM3_NAME,
               COM3.DEPT_CODE,

               d.data_source,
               D.LINE_CODE,               
               D.AXIS_NAME,
               
               SUM(NVL(D.PTD_BAL, 0)) PTD_BAL,               
               sum(case
                 when d.period_name = '${p_period_to}' then
                  NVL(D.END_BAL, 0)
               end) END_BAL
               
          FROM CUX_GL_FSG_DATA3 D,
               CUX_YYWD_LEDGER_V@apps_to_glkj.hfzq.com  COM3
        
         WHERE D.SEGMENT1 = COM3.COM3_CODE
           AND (('10' in ('${p_dept_type}') and COM3.DEPT_CODE = '0' AND
               D.SEGMENT2 <> '41126') OR
               ('20' in ('${p_dept_type}') and COM3.DEPT_CODE = '41126' AND
               D.SEGMENT2 = '41126') OR
               ('30' in ('${p_dept_type}') and COM3.DEPT_CODE = 'T'))
              
           AND D.LEDGER_ID IN ('${p_ledger}')

                  --add by lsx 20220222
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year and
                       nvl(com3.effective_end_year,
                           substr('${p_period}', 1, 4))
                           
          
           AND D.PERIOD_NAME between '${p_period}' and '${p_period_to}'
           AND D.CURRENCY_CODE = '${p_currency}'
           AND D.ACTUAL_FLAG = 'A'
           and d.data_version='${p_version}'

           AND COM3.COM1_CODE NOT IN ('1110', '6100', '7100', '8100', '9901')
           
         GROUP BY COM3.COM1_CODE,
                  COM3.COM1_NAME,
                  COM3.COM2_CODE,
                  COM3.COM2_NAME,
                  COM3.COM3_CODE,
                  COM3.COM3_NAME,
                  COM3.DEPT_CODE,

                  d.data_source,
                  D.LINE_CODE,
                  D.AXIS_NAME) T,
       CUX_IMA_LINE_BASIC_CODE@apps_to_glkj.hfzq.com L,
       CUX_IMA_REPORT_ITEM_CODE@apps_to_glkj.hfzq.com R
       
 WHERE 1 = 1
   AND T.LINE_CODE = L.LINE_CODE
   AND T.AXIS_NAME = R.ITEM_CODE
   AND r.SHOW_RULES2 = 'Y'
   
   ${if(len(p_item_code)=0, "", " AND R.ITEM_CODE IN ('"+p_item_code+"') ")}  
   
   ${if(len(p_com1)=0, "", " AND T.COM1_CODE IN ('"+p_com1+"') ")}  
   ${if(len(p_com2)=0, "", " AND T.COM2_CODE IN ('"+p_com2+"') ")}    
   ${if(len(p_com3)=0, "", " AND T.COM3_CODE IN ('"+p_com3+"') ")} 

group by 
       ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       L.LINE_CODE||'-'||L.LINE_NAME,
       AXIS_NAME
 
)


select *
  from (select *
          from base
         where 1 = 1
        ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
        
        union all
        
        select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
               ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
               ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
        
               '9999',
               '9999-汇总' line_name,
               axis_name,
               sum(ptd_bal) ptd_bal,
               sum(end_bal) end_bal
          from base
         where 1 = 1
           and  '9999' in ('${p_line_code}' )
         group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
                  ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
                  ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
                   axis_name)

ORDER BY line_code, 
${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
AXIS_NAME
]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="p_item_level" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_version"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[SELECT item_level
  FROM cux_ima_report_item_code
 WHERE 1 = 1
 AND SHOW_RULES1 = 'Y'
 AND DATA_VERSION='${p_version}' --ADD BY LSX 20230302
 order by item_level]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="数据(参数固化)" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_period_to"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[apps]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with temp as
(--账套：
select *
  from (SELECT l.LEDGER_ID,
               l.LEDGER_ID || ' ' || l.NAME || ' ' || l.DESCRIPTION ledger_name
          FROM GL_LEDGERS l
         where (l.NAME like 'A%' or l.NAME like 'O%' or l.NAME like 'D%')
           and l.LEDGER_ID not in (2184, 2061)
        union all
        select to_number(i.code) ledger_id,
               i.code || ' ' || i.name || ' ' || i.remark ledger_name
          from cux_ima_dim_info@apps_to_glkj.hfzq.com i
         where i.type_code = '140') l
 where 1 = 1
 order by LEDGER_ID, ledger_NAME
),
base as (SELECT
       COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,
       L.LINE_CODE,
       L.LINE_CODE||'-'||L.LINE_NAME LINE_NAME,
       AXIS_NAME,
       sum(decode(data_source, 'MA', 1, DECODE(R.CHANGE_SIGN, 'Y', -1, 1)) * nvl(PTD_BAL, 0)) PTD_BAL ,
       sum(decode(data_source, 'MA', 1, DECODE(R.CHANGE_SIGN, 'Y', -1, 1)) * nvl(END_BAL, 0)) END_BAL
  FROM (SELECT COM3.COM1_CODE COM1_CODE,
               COM3.COM1_CODE || '-' || COM3.COM1_NAME COM1_NAME,
               COM3.COM2_CODE COM2_CODE,
               COM3.COM2_CODE || '-' || COM3.COM2_NAME COM2_NAME,
               COM3.COM3_CODE COM3_CODE,
               COM3.COM3_CODE || '-' || COM3.COM3_NAME COM3_NAME,
               COM3.DEPT_CODE,

               d.data_source,
               D.LINE_CODE,               
               D.AXIS_NAME,
               
               SUM(NVL(D.PTD_BAL, 0)) PTD_BAL,               
               sum(case
                 when d.period_name = '${p_period_to}' then
                  NVL(D.END_BAL, 0)
               end) END_BAL
               
          FROM CUX_GL_FSG_DATA3 D,
               CUX_YYWD_LEDGER_V@apps_to_glkj.hfzq.com  COM3
        
         WHERE D.SEGMENT1 = COM3.COM3_CODE
           AND ((COM3.DEPT_CODE = '0' AND
               D.SEGMENT2 <> '41126') OR
               (COM3.DEPT_CODE = '41126' AND
               D.SEGMENT2 = '41126') OR
               (COM3.DEPT_CODE = 'T'))
              
           AND D.LEDGER_ID IN (select LEDGER_ID from temp where LEDGER_ID not in(2163)) -----剔除D账套

	        --add by lsx 20220222
	         and substr('2024-01', 1, 4) between
	             com3.effective_start_year and
	             nvl(com3.effective_end_year,
	                 substr('2024-01', 1, 4))

           --------add by lsx 20230525-------------
                   and substr('2024-01', 1, 4) between
                       com3.effective_start_year1 and
                       nvl(com3.effective_end_year1,
                           substr('2024-01', 1, 4))
                   and substr('2024-01', 1, 4) between
                       com3.effective_start_year2 and
                       nvl(com3.effective_end_year2,
                           substr('2024-01', 1, 4))
                    ------add by lsx 20230525 end ---------------
                    
          --add by lsx 20230310
          and d.is_restore_data in ('10') ----财务数据
          
           AND D.PERIOD_NAME between '2024-01' and '${p_period_to}'
           AND D.CURRENCY_CODE = 'CNY'    -----币种限制人民币
           AND D.ACTUAL_FLAG = 'A'
           and d.data_version='V2023-001'

           AND COM3.COM1_CODE NOT IN ('1110', '6100', '7100', '8100', '9901')
           
         GROUP BY COM3.COM1_CODE,
                  COM3.COM1_NAME,
                  COM3.COM2_CODE,
                  COM3.COM2_NAME,
                  COM3.COM3_CODE,
                  COM3.COM3_NAME,
                  COM3.DEPT_CODE,

                  d.data_source,
                  D.LINE_CODE,
                  D.AXIS_NAME) T,
       CUX_IMA_LINE_BASIC_CODE@apps_to_glkj.hfzq.com L,
       CUX_IMA_REPORT_ITEM_CODE@apps_to_glkj.hfzq.com R
       
 WHERE 1 = 1
   AND T.LINE_CODE = L.LINE_CODE
   AND T.AXIS_NAME = R.ITEM_CODE
   AND r.SHOW_RULES2 = 'Y'

   and R.data_version = 'V2023-001' --ADD BY LSX 20230302
   and r.item_level in ('1')
   AND R.ITEM_CODE IN ('100010')
  
group by 
COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,
       L.LINE_CODE,
       L.LINE_CODE||'-'||L.LINE_NAME,
       AXIS_NAME
 
)         
select * from
        (select COM1_CODE, COM1_NAME,
               '9999' line_code,
               '9999-汇总' line_name,
               axis_name,
               sum(end_bal)/10000 end_bal
          from base
         where 1 = 1
         group by COM1_CODE, COM1_NAME,axis_name
		 )
		 ORDER BY line_code, COM1_CODE, COM1_NAME, AXIS_NAME]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
<TableData name="ds2_投行" class="com.fr.data.impl.DBTableData">
<Desensitizations desensitizeOpen="false"/>
<Parameters>
<Parameter>
<Attributes name="p_level"/>
<O>
<![CDATA[2]]></O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_ledger"/>
<O>
<![CDATA[2021]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O>
<![CDATA[2025-01]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O>
<![CDATA[]]></O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O>
<![CDATA[10]]></O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O>
<![CDATA[1070]]></O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O>
<![CDATA[CNY]]></O>
</Parameter>
<Parameter>
<Attributes name="p_item_level"/>
<O>
<![CDATA[1]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O>
<![CDATA[1210]]></O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O>
<![CDATA[30]]></O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O>
<![CDATA[V2025-001]]></O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O>
<![CDATA[2025-01]]></O>
</Parameter>
</Parameters>
<Attributes maxMemRowCount="-1"/>
<Connection class="com.fr.data.impl.NameDatabaseConnection">
<DatabaseName>
<![CDATA[glkj]]></DatabaseName>
</Connection>
<Query>
<![CDATA[with base as (SELECT ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       L.LINE_CODE||'-'||L.LINE_NAME LINE_NAME,
       
       AXIS_NAME,
       
       sum(decode(data_source, 'MA', 1, DECODE(R.CHANGE_SIGN, 'Y', -1, 1)) * nvl(PTD_BAL, 0)) PTD_BAL ,
       sum(decode(data_source, 'MA', 1, DECODE(R.CHANGE_SIGN, 'Y', -1, 1)) * nvl(END_BAL, 0)) END_BAL
  FROM (SELECT COM3.COM1_CODE COM1_CODE,
               COM3.COM1_CODE || '-' || COM3.COM1_NAME COM1_NAME,
               COM3.COM2_CODE COM2_CODE,
               COM3.COM2_CODE || '-' || COM3.COM2_NAME COM2_NAME,
               COM3.COM3_CODE COM3_CODE,
               COM3.COM3_CODE || '-' || COM3.COM3_NAME COM3_NAME,
               COM3.DEPT_CODE,

               d.data_source,
               D.LINE_CODE,               
               D.AXIS_NAME,
               
               SUM(NVL(D.PTD_BAL, 0)) PTD_BAL,               
               sum(case
                 when d.period_name = '${p_period_to}' then
                  NVL(D.END_BAL, 0)
               end) END_BAL
               
          FROM HFGLKJ.CUX_GL_FSG_DATA3 D
		  left join
               HFGLKJ.CUX_YYWD_LEDGER_V  COM3
          on D.SEGMENT1 = COM3.COM3_CODE
          where (('10' in ('${p_dept_type}') and COM3.DEPT_CODE = '0' AND
               D.SEGMENT2 <> '41126') OR
               ('20' in ('${p_dept_type}') and COM3.DEPT_CODE = '41126' AND
               D.SEGMENT2 = '41126') OR
               ('30' in ('${p_dept_type}') and COM3.DEPT_CODE = 'T'))
              
           AND D.LEDGER_ID IN ('${p_ledger}')

	        --add by lsx 20220222
	         and substr('${p_period}', 1, 4) between
	             com3.effective_start_year and
	             nvl(com3.effective_end_year,
	                 substr('${p_period}', 1, 4))

           --------add by lsx 20230525-------------
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year1 and
                       nvl(com3.effective_end_year1,
                           substr('${p_period}', 1, 4))
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year2 and
                       nvl(com3.effective_end_year2,
                           substr('${p_period}', 1, 4))
                    ------add by lsx 20230525 end ---------------
                    
          --add by lsx 20230310
          ${if(len(p_source)=0, "", " and d.is_restore_data in ('"+p_source+"') ")}
          
           AND D.PERIOD_NAME between '${p_period}' and '${p_period_to}'
           AND D.CURRENCY_CODE = '${p_currency}'
           AND D.ACTUAL_FLAG = 'A'
           and d.data_version='${p_version}'

           AND COM3.COM1_CODE NOT IN ('1110', '6100', '7100', '8100', '9901')
           
         GROUP BY COM3.COM1_CODE,
                  COM3.COM1_NAME,
                  COM3.COM2_CODE,
                  COM3.COM2_NAME,
                  COM3.COM3_CODE,
                  COM3.COM3_NAME,
                  COM3.DEPT_CODE,

                  d.data_source,
                  D.LINE_CODE,
                  D.AXIS_NAME
		union all 
        SELECT COM3.COM1_CODE COM1_CODE,
               COM3.COM1_CODE || '-' || COM3.COM1_NAME COM1_NAME,
               COM3.COM2_CODE COM2_CODE,
               COM3.COM2_CODE || '-' || COM3.COM2_NAME COM2_NAME,
               COM3.COM3_CODE COM3_CODE,
               COM3.COM3_CODE || '-' || COM3.COM3_NAME COM3_NAME,
               COM3.DEPT_CODE,

               d.data_source,
               case when D.period_name>='2024-10' AND D.LINE_CODE='1060' then '2070' else D.LINE_CODE end LINE_CODE,    
               '100611' AXIS_NAME,
               
               SUM(NVL(D.PTD_BAL, 0)) PTD_BAL,               
               sum(case
                 when d.period_name = '${p_period_to}' then
                  NVL(D.END_BAL, 0)
               end) END_BAL
               
          FROM hfglkj.CUX_GL_FSG_DATA3 D
          left join  hfglkj.CUX_YYWD_LEDGER_V  COM3
          on D.SEGMENT1 = COM3.COM3_CODE
          where (('10' in ('${p_dept_type}') and COM3.DEPT_CODE = '0' AND
               D.SEGMENT2 <> '41126') OR
               ('20' in ('${p_dept_type}') and COM3.DEPT_CODE = '41126' AND
               D.SEGMENT2 = '41126') OR
               ('30' in ('${p_dept_type}') and COM3.DEPT_CODE = 'T'))
              
           AND D.LEDGER_ID IN ('${p_ledger}')

	         and substr('${p_period}', 1, 4) between
	             com3.effective_start_year and
	             nvl(com3.effective_end_year,
	                 substr('${p_period}', 1, 4))
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year1 and
                       nvl(com3.effective_end_year1,
                           substr('${p_period}', 1, 4))
                   and substr('${p_period}', 1, 4) between
                       com3.effective_start_year2 and
                       nvl(com3.effective_end_year2,
                           substr('${p_period}', 1, 4))
          ${if(len(p_source)=0, "", " and d.is_restore_data in ('"+p_source+"') ")}
          
           AND D.PERIOD_NAME between '${p_period}' and '${p_period_to}'
           AND D.CURRENCY_CODE = '${p_currency}'
           AND D.ACTUAL_FLAG = 'A'
           and d.data_version='${p_version}'
           and d.segment6 = '311116'
		   and d.AXIS_NAME = '100610'
           AND COM3.COM1_CODE NOT IN ('1110', '6100', '7100', '8100', '9901')
           
         GROUP BY COM3.COM1_CODE,
                  COM3.COM1_NAME,
                  COM3.COM2_CODE,
                  COM3.COM2_NAME,
                  COM3.COM3_CODE,
                  COM3.COM3_NAME,
                  COM3.DEPT_CODE,

                  d.data_source,
                  case when D.period_name>='2024-10' AND D.LINE_CODE='1060' then '2070' else D.LINE_CODE end		  
				  
				  ) T
       left join HFGLKJ.CUX_IMA_LINE_BASIC_CODE L
       on  T.LINE_CODE = L.LINE_CODE
	   left join HFGLKJ.CUX_IMA_REPORT_ITEM_CODE R
       on T.AXIS_NAME = R.ITEM_CODE
 WHERE L.LINE_CODE='1070'
   AND r.SHOW_RULES2 = 'Y'

   and R.data_version = '${p_version}' --ADD BY LSX 20230302
   ${if(len(p_item_level)=0, "", " and r.item_level in ("+p_item_level+") ")}
   
   ${if(len(p_item_code)=0, "", " AND R.ITEM_CODE IN ('"+p_item_code+"') ")}  
   
   ${if(len(p_com1)=0, "", " AND T.COM1_CODE IN ('"+p_com1+"') ")}  
   ${if(len(p_com2)=0, "", " AND T.COM2_CODE IN ('"+p_com2+"') ")}    
   ${if(len(p_com3)=0, "", " AND T.COM3_CODE IN ('"+p_com3+"') ")} 

group by 
       ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       L.LINE_CODE,
       L.LINE_CODE||'-'||L.LINE_NAME,
       AXIS_NAME
 
)
-- 25版KPI利润（考核利润）根据所得税费用调整(一级分公司)
,base_2025 as(

select ${if(p_level='1', "t1.COM1_CODE, t1.COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       t1.LINE_CODE,
       t1.LINE_NAME,
       t1.AXIS_NAME,
       sum(nvl(t1.ptd_bal,0)) ptd_bal,
       sum(nvl(t1.end_bal,0)) end_bal
from
(select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       LINE_CODE,
       LINE_NAME,
       '102030' AXIS_NAME,
	   ptd_bal,
	   end_bal
from base
where ${if(p_level='1', " axis_name='102010' ", " 1=0 ")}
union all
select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       LINE_CODE,
       LINE_NAME,
       '102030' AXIS_NAME,
       case when ptd_bal>0 then 0 else ptd_bal end ptd_bal,
	   case when end_bal>0 then 0 else end_bal end end_bal
from base l
where 
${if(p_level='1', " axis_name='102000' ", " 1=0 ")}) t1
group by ${if(p_level='1', "t1.COM1_CODE, t1.COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
       t1.LINE_CODE,
       t1.LINE_NAME,
       t1.AXIS_NAME
)



select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
               ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
               ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
        line_code,
        line_name,
        axis_name,

        round(sum(ptd_bal), 2) ptd_bal,
        round(sum(end_bal), 2) end_bal        
        
  from (select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME,
       AXIS_NAME,
	  ptd_bal,
       end_bal	
          from base
         where 1 = 1
        ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
        ${if(p_version='V2025-001', "AND axis_name <> '102030'", "")}

        union all
		-- 固定费用不包含客户维护费
		select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME,
       '101230' AXIS_NAME,
	   -ptd_bal ptd_bal,
       -end_bal	end_bal
          from base
         where 1 = 1
        ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
        ${if(p_version='V2025-001', "AND axis_name = '101920'", "AND 1=0")}
		
		union all
		-- 其他资金成本
		select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME,
       '100612' AXIS_NAME,
	   ptd_bal ptd_bal,
       end_bal	end_bal
          from base
         where 1 = 1
        ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
        ${if(p_version='V2025-001', "AND axis_name = '100610'", "AND 1=0")}
		union all
		-- 其他资金成本
		select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME,
       '100612' AXIS_NAME,
	   -ptd_bal ptd_bal,
       -end_bal	end_bal
          from base
         where 1 = 1
        ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")}  
        ${if(p_version='V2025-001', "AND axis_name = '100611'", "AND 1=0")}
		
		union all

       select ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
       ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
       ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}

       LINE_CODE,
       LINE_NAME,
       AXIS_NAME,
	   ptd_bal,
       end_bal	
          from base_2025
         where 1 = 1
        ${if(len(p_line_code)=0, "", " AND LINE_CODE IN ('"+p_line_code+"') ")} 
        ${if(p_version='V2025-001', "", " and 1=0")}
       ) t1
group by ${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
         ${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
         ${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
        line_code,
        line_name,
        axis_name
ORDER BY decode(LINE_CODE,'1010',1,'1020',2,'2080',3,'1030',4,'1040',5,'1045',6,'1050',7,'1060',8,'1070',9,'2050',10,'2060',11,'1080',12,'1090',13,'1110',14,'1190',15,'2010',16,'2020',17,'2030',18,'2040',19,'2070',20),
${if(p_level='1', "COM1_CODE, COM1_NAME, ", "")}
${if(p_level='2', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, ", "")}
${if(p_level='3', "COM1_CODE, COM1_NAME, COM2_CODE, COM2_NAME, COM3_CODE, COM3_NAME, DEPT_CODE,", "")}
AXIS_NAME]]></Query>
<PageQuery>
<![CDATA[]]></PageQuery>
</TableData>
</TableDataMap>
<ReportWebAttr>
<ServerPrinter/>
<WebViewContent>
<ToolBars>
<ToolBarManager>
<Location>
<Embed position="1"/>
</Location>
<ToolBar>
<Widget class="com.fr.report.web.button.ExcelO">
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[${i18n('Fine-Engine_Export_Excel_Simple')}]]></Text>
<Hotkeys>
<![CDATA[]]></Hotkeys>
<IconName>
<![CDATA[excel]]></IconName>
</Widget>
</ToolBar>
</ToolBarManager>
</ToolBars>
<SortFuncCheck check="true"/>
<ConditionFuncCheck check="true"/>
<ListFuncCheck check="true"/>
</WebViewContent>
</ReportWebAttr>
<ElementCaseMobileAttr>
<ElementCaseMobileAttrProvider horizontal="1" vertical="1" zoom="true" refresh="false" isUseHTML="false" isMobileCanvasSize="false" appearRefresh="false" allowFullScreen="false" allowDoubleClickOrZoom="true" functionalWhenUnactivated="false"/>
</ElementCaseMobileAttr>
<Report class="com.fr.report.worksheet.WorkSheet" name="sheet1">
<ReportPageAttr>
<HR F="0" T="12"/>
<FR/>
<HC F="0" T="5"/>
<FC/>
<UPFCR COLUMN="true" ROW="true"/>
<USE REPEAT="false" PAGE="false" WRITE="false"/>
</ReportPageAttr>
<ColumnPrivilegeControl/>
<RowPrivilegeControl/>
<RowHeight defaultValue="723900">
<![CDATA[288000,1440000,288000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,1008000,723900]]></RowHeight>
<ColumnWidth defaultValue="2743200">
<![CDATA[576000,1440000,2160000,12458700,1440000,1440000,4032000,4032000,4032000,4032000,2743200]]></ColumnWidth>
<CellElementList>
<C c="0" r="0">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="1" cs="5" s="0">
<O>
<![CDATA[分公司考核利润表]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="6" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="7" r="1" s="1">
<PrivilegeControl/>
<Expand/>
</C>
<C c="8" r="1" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="1" r="3" rs="10" s="3">
<O>
<![CDATA[行次]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="2" r="3" rs="10" s="3">
<O>
<![CDATA[表项代码]]></O>
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="3" rs="10" s="3">
<O>
<![CDATA[行项目]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="3" rs="10" s="3">
<O>
<![CDATA[层级]]></O>
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="3" rs="10" s="3">
<O>
<![CDATA[等级]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="3" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="LINE_CODE"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="3" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="LINE_CODE"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[LINE_CODE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[1070]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="4" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="LINE_NAME"/>
<Condition class="com.fr.data.condition.ListCondition"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="4" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="LINE_NAME"/>
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[LINE_CODE]]></CNAME>
<Compare op="0">
<O>
<![CDATA[1070]]></O>
</Compare>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="5" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="COM1_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="5" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="COM1_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="6" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="COM1_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="6" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="COM1_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="7" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="COM2_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="7" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="COM2_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="8" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="COM2_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$p_level = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="8" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="COM2_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$p_level = 1]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="9" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="COM3_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="9" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="COM3_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="10" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="COM3_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$p_level = 1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$p_level = 2]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="10" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="COM3_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$p_level = 1]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[$p_level = 2]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="11" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="DEPT_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="11" cs="2" s="3">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="DEPT_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition"/>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.RowHeightHighlightAction"/>
</Highlight>
</HighlightList>
<Expand dir="1">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="12" s="3">
<O>
<![CDATA[本期金额]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[FIND("10",$p_amt_type) = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="12" s="3">
<O>
<![CDATA[本年金额]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[FIND("20",$p_amt_type) = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand/>
</C>
<C c="8" r="12" s="3">
<O>
<![CDATA[本期金额]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[FIND("10",$p_amt_type) = 0]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[AND(FIND("V2024-001",$p_version) = 0 ,FIND("V2025-001",$p_version) = 0)]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="9" r="12" s="3">
<O>
<![CDATA[本年金额]]></O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[FIND("20",$p_amt_type) = 0]]></Formula>
</Condition>
</JoinCondition>
<JoinCondition join="1">
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[AND(FIND("V2024-001",$p_version) = 0 ,FIND("V2025-001",$p_version) = 0)]]></Formula>
</Condition>
</JoinCondition>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.ColWidthHighlightAction"/>
</Highlight>
</HighlightList>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="1" r="13" s="4">
<O t="DSColumn">
<Attributes dsName="ds1_item" columnName="RN"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper">
<Attr divideMode="1"/>
</RG>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="2" r="13" s="4">
<O t="DSColumn">
<Attributes dsName="ds1_item" columnName="ITEM_CODE"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="13" s="5">
<O t="DSColumn">
<Attributes dsName="ds1_item" columnName="ITEM_NAME"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="4" r="13" s="4">
<O t="DSColumn">
<Attributes dsName="ds1_item" columnName="ITEM_LEVEL"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
</O>
<PrivilegeControl/>
<Expand dir="0"/>
</C>
<C c="5" r="13" s="4">
<O t="DSColumn">
<Attributes dsName="ds1_item" columnName="CLS3"/>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Parameters/>
<cellSortAttr/>
</O>
<PrivilegeControl/>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="6" r="13" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="PTD_BAL"/>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[AXIS_NAME]]></CNAME>
<Compare op="0">
<ColumnRow column="2" row="13"/>
</Compare>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[LINE_CODE]]></CNAME>
<Compare op="0">
<ColumnRow column="7" row="3"/>
</Compare>
</Condition>
</JoinCondition>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$/$p_unit]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[F14 <> 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="72" underline="17">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="p_ledger"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_ledger]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period_to]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_currency]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C14]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_version]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_amt_type"/>
<O>
<![CDATA[10]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G6, $p_com1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G8, $p_com2)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G10, $p_com3)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G12]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_dept_type]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_source]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_unit"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_unit]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept1"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HFGLKJ/LINE_PROFIT/HF_LINE_CHECK_PROFIT_DEPT_1.cpt&op=view]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[试算平衡表]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[F14 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="72" underline="17">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="p_ledger"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_ledger]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period_to]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_currency]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C14]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_version]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_amt_type"/>
<O>
<![CDATA[10]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G6, $p_com1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G8, $p_com2)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G10, $p_com3)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G12]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_dept_type]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_source]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_unit"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_unit]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept1"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HFGLKJ/LINE_PROFIT/HF_LINE_CHECK_PROFIT_TRIAL_BALANCE.cpt&op=view]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="7" r="13" s="6">
<O t="DSColumn">
<Attributes dsName="ds1" columnName="END_BAL"/>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[AXIS_NAME]]></CNAME>
<Compare op="0">
<ColumnRow column="2" row="13"/>
</Compare>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[LINE_CODE]]></CNAME>
<Compare op="0">
<ColumnRow column="7" row="3"/>
</Compare>
</Condition>
</JoinCondition>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$/$p_unit]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[F14 <> 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="72" underline="17">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="p_ledger"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_ledger]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period_to]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_currency]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C14]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_version]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_amt_type"/>
<O>
<![CDATA[20]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G6, $p_com1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G8, $p_com2)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G10, $p_com3)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G12]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_dept_type]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_source]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_unit"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_unit]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept1"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HFGLKJ/LINE_PROFIT/HF_LINE_CHECK_PROFIT_DEPT_1.cpt&op=view]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[试算平衡表]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[F14 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="72" underline="17">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="p_ledger"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_ledger]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period_to]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_currency]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C14]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_version]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_amt_type"/>
<O>
<![CDATA[20]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G6, $p_com1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G8, $p_com2)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G10, $p_com3)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G12]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_dept_type]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_source]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_unit"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_unit]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept1"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HFGLKJ/LINE_PROFIT/HF_LINE_CHECK_PROFIT_TRIAL_BALANCE.cpt&op=view]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="8" r="13" s="6">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="PTD_BAL"/>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[AXIS_NAME]]></CNAME>
<Compare op="0">
<ColumnRow column="2" row="13"/>
</Compare>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[LINE_CODE]]></CNAME>
<Compare op="0">
<ColumnRow column="9" row="3"/>
</Compare>
</Condition>
</JoinCondition>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$/$p_unit]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[F14 <> 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="72" underline="17">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="p_ledger"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_ledger]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period_to]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_currency]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C14]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_version]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_amt_type"/>
<O>
<![CDATA[10]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G6, $p_com1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G8, $p_com2)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G10, $p_com3)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G12]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_dept_type]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_source]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_unit"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_unit]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept1"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HFGLKJ/LINE_PROFIT/HF_LINE_CHECK_PROFIT_DEPT_1.cpt&op=view]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[试算平衡表]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[F14 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="72" underline="17">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="p_ledger"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_ledger]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period_to]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_currency]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C14]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_version]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_amt_type"/>
<O>
<![CDATA[10]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G6, $p_com1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G8, $p_com2)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G10, $p_com3)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G12]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_dept_type]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_source]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_unit"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_unit]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept1"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HFGLKJ/LINE_PROFIT/HF_LINE_CHECK_PROFIT_TRIAL_BALANCE.cpt&op=view]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0" leftParentDefault="false" left="F14">
<cellSortAttr>
<sortExpressions/>
<sortHeader sortArea="I14"/>
</cellSortAttr>
</Expand>
</C>
<C c="9" r="13" s="6">
<O t="DSColumn">
<Attributes dsName="ds2_投行" columnName="END_BAL"/>
<Condition class="com.fr.data.condition.ListCondition">
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[AXIS_NAME]]></CNAME>
<Compare op="0">
<ColumnRow column="2" row="13"/>
</Compare>
</Condition>
</JoinCondition>
<JoinCondition join="0">
<Condition class="com.fr.data.condition.CommonCondition">
<CNUMBER>
<![CDATA[0]]></CNUMBER>
<CNAME>
<![CDATA[LINE_CODE]]></CNAME>
<Compare op="0">
<ColumnRow column="9" row="3"/>
</Compare>
</Condition>
</JoinCondition>
</Condition>
<Complex/>
<RG class="com.fr.report.cell.cellattr.core.group.FunctionGrouper"/>
<Result>
<![CDATA[$$$/$p_unit]]></Result>
<Parameters/>
<cellSortAttr>
<sortExpressions/>
</cellSortAttr>
</O>
<PrivilegeControl/>
<HighlightList>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[条件属性1]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[F14 <> 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="72" underline="17">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="p_ledger"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_ledger]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period_to]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_currency]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C14]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_version]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_amt_type"/>
<O>
<![CDATA[20]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G6, $p_com1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G8, $p_com2)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G10, $p_com3)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G12]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_dept_type]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_source]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_unit"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_unit]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept1"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HFGLKJ/LINE_PROFIT/HF_LINE_CHECK_PROFIT_DEPT_1.cpt&op=view]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
<Highlight class="com.fr.report.cell.cellattr.highlight.DefaultHighlight">
<Name>
<![CDATA[试算平衡表]]></Name>
<Condition class="com.fr.data.condition.FormulaCondition">
<Formula>
<![CDATA[F14 = 0]]></Formula>
</Condition>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.FRFontHighlightAction">
<FRFont name="微软雅黑" style="0" size="72" underline="17">
<foreground>
<FineColor color="-16776961" hor="-1" ver="-1"/>
</foreground>
</FRFont>
</HighlightAction>
<HighlightAction class="com.fr.report.cell.cellattr.highlight.HyperlinkHighlightAction">
<NameJavaScriptGroup>
<NameJavaScript name="网络报表1">
<JavaScript class="com.fr.js.ReportletHyperlink">
<JavaScript class="com.fr.js.ReportletHyperlink">
<Parameters>
<Parameter>
<Attributes name="p_ledger"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_ledger]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_period_to"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_period_to]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_currency"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_currency]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_line_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G4]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_item_code"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=C14]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_version"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_version]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_amt_type"/>
<O>
<![CDATA[20]]></O>
</Parameter>
<Parameter>
<Attributes name="p_com1"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G6, $p_com1)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com2"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G8, $p_com2)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_com3"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=NVL(G10, $p_com3)]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=G12]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_level"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_level]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept_type"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_dept_type]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_source"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_source]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_unit"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=$p_unit]]></Attributes>
</O>
</Parameter>
<Parameter>
<Attributes name="p_dept1"/>
<O>
<![CDATA[]]></O>
</Parameter>
</Parameters>
<TargetFrame>
<![CDATA[_blank]]></TargetFrame>
<Features width="600" height="400"/>
<ReportletName showPI="true" byPost="true">
<![CDATA[/HFGLKJ/LINE_PROFIT/HF_LINE_CHECK_PROFIT_TRIAL_BALANCE.cpt&op=view]]></ReportletName>
<Attr>
<DialogAttr class="com.fr.js.ReportletHyperlinkDialogAttr">
<O>
<![CDATA[]]></O>
<Location center="true"/>
</DialogAttr>
</Attr>
</JavaScript>
</JavaScript>
</NameJavaScript>
</NameJavaScriptGroup>
</HighlightAction>
</Highlight>
</HighlightList>
<Expand dir="0">
<cellSortAttr/>
</Expand>
</C>
<C c="3" r="14" s="2">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
<C c="5" r="15" s="2">
<PrivilegeControl/>
<Expand/>
</C>
<C c="3" r="17" s="2">
<PrivilegeControl/>
<Expand>
<cellSortAttr/>
</Expand>
</C>
</CellElementList>
<ReportAttrSet>
<ReportSettings headerHeight="0" footerHeight="0">
<PaperSetting>
<PaperSize width="1355040000" height="287568000"/>
<Margin top="986400" left="2743200" bottom="986400" right="2743200"/>
</PaperSetting>
<FollowingTheme background="false"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
</ReportSettings>
<Header reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Header>
<Footer reportPageType="0">
<Background name="NullBackground"/>
<LeftList/>
<CenterList/>
<RightList/>
</Footer>
</ReportAttrSet>
<PrivilegeControl/>
</Report>
<ReportParameterAttr>
<Attributes showWindow="true" delayPlaying="true" windowPosition="1" align="0" useParamsTemplate="false" currentIndex="4"/>
<PWTitle>
<![CDATA[参数]]></PWTitle>
<ParameterUI class="com.fr.form.main.parameter.FormParameterUI">
<Parameters/>
<Layout class="com.fr.form.ui.container.WParameterLayout">
<Listener event="afterinit" name="权限判断">
<JavaScript class="com.fr.js.JavaScriptImpl">
<Parameters>
<Parameter>
<Attributes name="para_auth"/>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=if(
ISNULL(sql("hfzj", "select 1 from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = " + $fine_username + "", 1))
, 0
, sql("hfzj", "select 1 from hfglkj.tbb_glkj_final_auth where p_type = '分支机构考核利润' and staff_id = " + $fine_username + "", 1)
)]]></Attributes>
</O>
</Parameter>
</Parameters>
<Content>
<![CDATA[if (para_auth != 1) {
	for (var control in this.options.form.name_widgets) {
		this.options.form.getWidgetByName(control).setVisible(false);
	}
}]]></Content>
</JavaScript>
</Listener>
<WidgetName name="para"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<FollowingTheme borderStyle="false"/>
<Margin top="0" left="0" bottom="0" right="0"/>
<Border>
<border style="0" borderRadius="0" type="0" borderStyle="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
<WidgetTitle>
<O>
<![CDATA[新建标题]]></O>
<FRFont name="SimSun" style="0" size="72"/>
<Position pos="0"/>
</WidgetTitle>
<Alpha alpha="1.0"/>
</Border>
<Background name="ColorBackground">
<color>
<FineColor color="-526086" hor="-1" ver="-1"/>
</color>
</Background>
<LCAttr vgap="0" hgap="0" compInterval="0"/>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.TextEditor">
<WidgetName name="b1"/>
<WidgetID widgetID="d29605f5-5a1a-4228-9bff-f5ed81f2759d"/>
<WidgetAttr invisible="true" aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName="textEditor0"/>
<PrivilegeControl/>
</WidgetAttr>
<TextAttr/>
<Reg class="com.fr.form.ui.reg.NoneReg"/>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SQL("Impala","  SELECT case when staf_main_ins_name regexp '公司领导|业务巡视员|公司首席|总部' then '总部'
            when staf_main_ins_name regexp '福州分公司' then '福州分公司'
            when staf_main_ins_name regexp '宁德分公司' then '宁德分公司'
            when staf_main_ins_name regexp '南平分公司' then '南平分公司'
            when staf_main_ins_name regexp '泉州分公司' then '泉州分公司'
            when staf_main_ins_name regexp '三明分公司' then '三明分公司'
            when staf_main_ins_name regexp '莆田分公司' then '莆田分公司'
            when staf_main_ins_name regexp '厦门分公司' then '厦门分公司'
            when staf_main_ins_name regexp '漳州分公司' then '漳州分公司'
            when staf_main_ins_name regexp '龙岩分公司' then '龙岩分公司'
            when staf_main_ins_name regexp '北京分公司' then '北京分公司'
            when staf_main_ins_name regexp '上海分公司' then '上海分公司'
            when staf_main_ins_name regexp '浙江分公司' then '浙江分公司'
            when staf_main_ins_name regexp '华南分公司' then '华南分公司'
            when staf_main_ins_name regexp '山东分公司' then '山东分公司'
            when staf_main_ins_name regexp '西北分公司' then '西北分公司'
            when staf_main_ins_name regexp '江苏分公司' then '江苏分公司'
            when staf_main_ins_name regexp '华中分公司' then '华中分公司'
            when staf_main_ins_name regexp '西南分公司' then '西南分公司'
            when staf_main_ins_name regexp '东北分公司' then '东北分公司'
            when staf_main_ins_name regexp '苏州分公司' then '苏州分公司'
            when staf_main_ins_name regexp '广东分公司' then '广东分公司'
            when staf_main_ins_name regexp '重庆分公司' then '重庆分公司'
            when staf_main_ins_name regexp '宁波分公司' then '宁波分公司'
            when staf_main_ins_name regexp '河北分公司' then '河北分公司'
            when staf_main_ins_name regexp '新疆分公司' then '新疆分公司'
            when staf_main_ins_name regexp '河南分公司' then '河南分公司'
            when staf_main_ins_name regexp '湖南分公司' then '湖南分公司'
            end as bmzw
from ods.ods_ehr_staf_base_info_df   where cast(staf_no as string)= '" + $fine_username + "'
and ds in(select max(ds) from ods.ods_ehr_staf_base_info_df )",1)]]></Attributes>
</O>
</widgetValue>
<MobileScanCodeAttr scanCode="true" textInputMode="0" isSupportManual="true" isSupportScan="true" isSupportNFC="false"/>
</InnerWidget>
<BoundsAttr x="1005" y="10" width="18" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="p_unit"/>
<LabelName name="单位："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择币种]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="1" value="元"/>
<Dict key="10000" value="万元"/>
<Dict key="1000000" value="百万元"/>
<Dict key="100000000" value="亿元"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[1]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="906" y="65" width="85" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelp_unit"/>
<LabelName name="金额："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[单位：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="861" y="65" width="45" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label1p_item_level"/>
<LabelName name="数据来源："/>
<WidgetID widgetID="b7d790c8-7934-4ec5-813f-a4af32fe2c98"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[表项层级：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="336" y="65" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_item_level"/>
<LabelName name="表项层级："/>
<WidgetID widgetID="b7401b43-fdac-4596-aa6a-9d290061c7b0"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="-1.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ITEM_LEVEL" viName="ITEM_LEVEL"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_item_level]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sql("glkj", " SELECT distinct item_level
  FROM cux_ima_report_item_code
 WHERE 1 = 1
 AND SHOW_RULES1 = 'Y'
 AND DATA_VERSION='"+$p_version+"'
 order by item_level", 1)]]></Attributes>
</O>
</widgetValue>
<RAAttr isArray="false"/>
</InnerWidget>
<BoundsAttr x="416" y="65" width="90" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_source"/>
<LabelName name="数据来源："/>
<WidgetID widgetID="2c2a7cb3-9da6-4919-816d-a34b6b0a97c6"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="10" value="财务数据 "/>
<Dict key="20" value="还原数据 "/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[10]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="236" y="65" width="90" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label0_c_c_c"/>
<LabelName name="币种："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[数据来源：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="156" y="65" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_dept_type"/>
<LabelName name="类型："/>
<WidgetID widgetID="46fd1e80-8ebe-4b6a-a443-63775c20eda5"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[类型不能为空]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="TYPE1" viName="NAME1"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_dept_type]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[10\',\'20\',\'30]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="840" y="37" width="85" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label2"/>
<WidgetID widgetID="7bfabb55-f1b8-4f89-94db-bc279274e49e"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[类型：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="795" y="37" width="45" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="p_version"/>
<LabelName name="版本："/>
<WidgetID widgetID="9612356b-8bb5-4fba-8188-447f4df94257"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="CODE" viName="CODE"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_version]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sql("glkj", " select distinct 'V2025-001'
  from cux_ima_dim_info i
 where i.type_code = '130' ", 1)]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="55" y="65" width="90" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label1"/>
<WidgetID widgetID="9014ef9b-5e97-4f67-b54c-b15b38c6eabe"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[版本：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="10" y="65" width="45" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label1p_item"/>
<LabelName name="表项层级："/>
<WidgetID widgetID="b4385dd4-3641-4108-9da6-4baca6dc3ded"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[表项：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="516" y="65" width="70" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_item_code"/>
<LabelName name="表项："/>
<WidgetID widgetID="d52fd61d-d52d-4f81-bc2c-6dd8ab092f05"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="ITEM_CODE" viName="ITEM_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_item_code]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="586" y="65" width="260" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelp_period_to"/>
<LabelName name="期间自："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[期间至：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="511" y="10" width="65" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="p_period_to"/>
<LabelName name="期间至："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择期间]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="PERIOD_NAME" viName="PERIOD_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_period_to]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sql("apps", " select case
         when to_number(substr(period_name, 6)) - 1 > 0 then
          substr(period_name, 1, 5) ||
          lpad(to_char(to_number(substr(period_name, 6)) - 1), 2, '0')
         else
          to_char(to_number(substr(period_name, 1, 4) - 1)) || '-13'
       end period_name
  from (select max(p.PERIOD_NAME) period_name
          from gl_period_statuses p
         where p.CLOSING_STATUS in ('C', 'O')
           and p.APPLICATION_ID = 101
           and p.SET_OF_BOOKS_ID = 2021)
 ", 1)]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="576" y="10" width="90" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelp_ledger"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[账套：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="10" y="10" width="45" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_ledger"/>
<LabelName name="账套："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择账套]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="LEDGER_ID" viName="LEDGER_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_ledger]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=SQL("apps", " 
select *
  from (SELECT l.LEDGER_ID
          FROM GL_LEDGERS l
         where (l.NAME like 'A%' or l.NAME like 'O%' or l.NAME like 'D%')
           and l.LEDGER_ID not in (2184, 2061)
        union all
        select to_number(i.code) ledger_id
          from cux_ima_dim_info@apps_to_glkj.hfzq.com i
         where i.type_code = '140') l
 where 1 = 1
   and exists
 (select *
          from (select distinct r.ledger_id
                  from cux_ima_role_maintain@apps_to_glkj.hfzq.com r
                 where r.role_name in
                       (select regexp_substr('"+CONCATENATE($fine_role)+"',
                                             '[^,]A+',
                                             1,
                                             level,
                                             'i')
                          from dual
                        connect by level <=
                                   length('"+CONCATENATE($fine_role)+"}') -
                                   length(regexp_replace('"+CONCATENATE($fine_role)+"',
                                                         ',',
                                                         '')) + 1)) t
         where l.ledger_id =
               decode(t.ledger_id, 'T', l.ledger_id, t.ledger_id)
        
        )


   ", 1)]]></Attributes>
</O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="55" y="10" width="271" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelp_period"/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[期间自：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="336" y="10" width="60" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="p_period"/>
<LabelName name="期间："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择期间]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="PERIOD_NAME" viName="PERIOD_NAME"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_period_from]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes reserveExecute="true">
<![CDATA[=sql("apps", " select case
         when to_number(substr(period_name, 6)) - 1 > 0 then
          substr(period_name, 1, 5) ||
          lpad(to_char(to_number(substr(period_name, 6)) - 1), 2, '0')
         else
          to_char(to_number(substr(period_name, 1, 4) - 1)) || '-13'
       end period_name
  from (select max(p.PERIOD_NAME) period_name
          from gl_period_statuses p
         where p.CLOSING_STATUS in ('C', 'O')
           and p.APPLICATION_ID = 101
           and p.SET_OF_BOOKS_ID = 2021)
 ", 1)]]></Attributes>
</O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="396" y="10" width="90" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelp_currency"/>
<LabelName name="金额："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[币种：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="936" y="37" width="45" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="p_currency"/>
<LabelName name="币种："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择币种]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.CustomDictionary">
<CustomDictAttr>
<Dict key="CNY" value="CNY"/>
</CustomDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[CNY]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="981" y="37" width="85" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.parameter.FormSubmitButton">
<WidgetName name="formSubmit0"/>
<LabelName name="层级："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Text>
<![CDATA[查询]]></Text>
<Hotkeys>
<![CDATA[enter]]></Hotkeys>
</InnerWidget>
<BoundsAttr x="1002" y="65" width="64" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="labelp_amt_type"/>
<LabelName name="条线："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[金额：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="846" y="10" width="45" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_amt_type"/>
<LabelName name="金额："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="TYPE1" viName="NAME1"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_type]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[20]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="890" y="10" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label0"/>
<LabelName name="二级分公司："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[分支机构：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="516" y="37" width="70" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_com3"/>
<LabelName name="条线："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="COM_CODE" viName="COM_NAME2"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_com3]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="586" y="37" width="199" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelp_level"/>
<LabelName name="版本："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[层级：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="10" y="37" width="45" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboBox">
<WidgetName name="p_level"/>
<LabelName name="层级："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择层级]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="TYPE1" viName="NAME1"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_level]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[1]]></O>
</widgetValue>
</InnerWidget>
<BoundsAttr x="55" y="37" width="90" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelp_com1"/>
<LabelName name="条线："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[一级分公司：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="156" y="36" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_com1"/>
<LabelName name="一级分公司："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择一级分公司]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="COM_CODE" viName="COM_NAME2"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_com1]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O t="XMLable" class="com.fr.base.Formula">
<Attributes>
<![CDATA[=sql("glkj", "
select com_code
  from cux_yywd_com c
 where com_level = 1
 AND COM_CODE NOT IN ('1110', '6100', '7100', '8100', '9901','6511')
and exists (select *
          from (select distinct r.com1_code
                  from cux_ima_role_maintain r
                 where r.role_name in
                       (select regexp_substr('"+CONCATENATE($fine_role)+"',
                                             '[^,]A+',
                                             1,
                                             level,
                                             'i')
                          from dual
                        connect by level <=
                                   length('"+CONCATENATE($fine_role)+"') -
                                   length(regexp_replace('"+CONCATENATE($fine_role)+"',
                                                         ',',
                                                         '')) + 1)) t
         where c.com_code =
               decode(t.com1_code, 'T', c.com_code, t.com1_code)
        
        )
     and substr('${p_period}', 1, 4) between c.effective_start_year and
       nvl(c.effective_end_year, substr('${p_period}', 1, 4))	
", 1)]]></Attributes>
</O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="235" y="36" width="91" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="Labelp_com2"/>
<LabelName name="一级分公司："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[二级分公司：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="336" y="36" width="80" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_com2"/>
<LabelName name="二级分公司："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="COM_CODE" viName="COM_NAME2"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_com2]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue/>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="416" y="36" width="90" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.Label">
<WidgetName name="label0"/>
<LabelName name="期间至："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<widgetValue>
<O>
<![CDATA[条线：]]></O>
</widgetValue>
<LabelAttr verticalcenter="true" textalign="4" autoline="true"/>
<FRFont name="微软雅黑" style="0" size="72"/>
<border style="0">
<color>
<FineColor color="-723724" hor="-1" ver="-1"/>
</color>
</border>
</InnerWidget>
<BoundsAttr x="676" y="10" width="45" height="21"/>
</Widget>
<Widget class="com.fr.form.ui.container.WAbsoluteLayout$BoundsWidget">
<InnerWidget class="com.fr.form.ui.ComboCheckBox">
<WidgetName name="p_line_code"/>
<LabelName name="条线："/>
<WidgetAttr aspectRatioLocked="false" aspectRatioBackup="0.0" description="">
<MobileBookMark useBookMark="false" bookMarkName="" frozen="false" index="-1" oldWidgetName=""/>
<PrivilegeControl/>
</WidgetAttr>
<EMSG>
<![CDATA[请选择条线]]></EMSG>
<allowBlank>
<![CDATA[false]]></allowBlank>
<CustomData>
<![CDATA[false]]></CustomData>
<Dictionary class="com.fr.data.impl.TableDataDictionary">
<FormulaDictAttr kiName="LINE_CODE" viName="LINE_NAME2"/>
<TableDataDictAttr>
<TableData class="com.fr.data.impl.NameTableData">
<Name>
<![CDATA[p_line_code]]></Name>
</TableData>
</TableDataDictAttr>
</Dictionary>
<widgetValue>
<O>
<![CDATA[]]></O>
</widgetValue>
<RAAttr delimiter="&apos;,&apos;" isArray="false"/>
</InnerWidget>
<BoundsAttr x="721" y="10" width="108" height="21"/>
</Widget>
<ShowBookmarks showBookmarks="false"/>
<Sorted sorted="false"/>
<MobileWidgetList>
<Widget widgetName="p_ledger"/>
<Widget widgetName="p_period"/>
<Widget widgetName="p_period_to"/>
<Widget widgetName="p_line_code"/>
<Widget widgetName="p_amt_type"/>
<Widget widgetName="b1"/>
<Widget widgetName="p_com1"/>
<Widget widgetName="p_com2"/>
<Widget widgetName="p_level"/>
<Widget widgetName="p_com3"/>
<Widget widgetName="p_dept_type"/>
<Widget widgetName="p_currency"/>
<Widget widgetName="p_version"/>
<Widget widgetName="p_source"/>
<Widget widgetName="p_item_level"/>
<Widget widgetName="p_item_code"/>
<Widget widgetName="p_unit"/>
<Widget widgetName="formSubmit0"/>
</MobileWidgetList>
<FrozenWidgets/>
<MobileBookMarkStyle class="com.fr.form.ui.mobile.impl.DefaultMobileBookMarkStyle"/>
<Display display="true"/>
<DelayDisplayContent delay="true"/>
<UseParamsTemplate use="false"/>
<paramFireStopEdit fireEvent="false"/>
<Position position="0"/>
<Design_Width design_width="1103"/>
<NameTagModified>
<TagModified tag="formSubmit0" modified="true"/>
<TagModified tag="p_level" modified="true"/>
<TagModified tag="p_item_code" modified="true"/>
<TagModified tag="p_amt_type" modified="true"/>
<TagModified tag="p_unit" modified="true"/>
<TagModified tag="p_ledger" modified="true"/>
<TagModified tag="p_period" modified="true"/>
<TagModified tag="p_com2" modified="true"/>
<TagModified tag="p_com3" modified="true"/>
<TagModified tag="p_source" modified="true"/>
<TagModified tag="p_line_code" modified="true"/>
<TagModified tag="p_currency" modified="true"/>
<TagModified tag="p_item_level" modified="true"/>
<TagModified tag="p_com1" modified="true"/>
<TagModified tag="p_dept_type" modified="true"/>
<TagModified tag="p_version" modified="true"/>
<TagModified tag="p_period_to" modified="true"/>
</NameTagModified>
<WidgetNameTagMap>
<NameTag name="formSubmit0" tag="层级："/>
<NameTag name="p_level" tag="层级："/>
<NameTag name="p_item_code" tag="表项："/>
<NameTag name="p_amt_type" tag="金额："/>
<NameTag name="p_unit" tag="单位："/>
<NameTag name="p_ledger" tag="账套："/>
<NameTag name="p_period" tag="期间："/>
<NameTag name="p_com2" tag="二级分公司："/>
<NameTag name="p_com3" tag="条线："/>
<NameTag name="p_source" tag="数据来源："/>
<NameTag name="p_line_code" tag="条线："/>
<NameTag name="p_currency" tag="币种："/>
<NameTag name="p_item_level" tag="表项层级："/>
<NameTag name="p_com1" tag="一级分公司："/>
<NameTag name="p_dept_type" tag="类型："/>
<NameTag name="p_version" tag="版本："/>
<NameTag name="p_period_to" tag="期间自："/>
</WidgetNameTagMap>
<ParamAttr class="com.fr.report.mobile.DefaultMobileParamStyle"/>
<ParamStyle class="com.fr.form.ui.mobile.impl.DefaultMobileParameterStyle"/>
<FollowingTheme background="false"/>
</Layout>
<DesignAttr width="1103" height="90"/>
</ParameterUI>
</ReportParameterAttr>
<StyleList>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="144"/>
<Background name="ColorBackground">
<color>
<FineColor color="-526085" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="144"/>
<Background name="ColorBackground">
<color>
<FineColor color="-526085" hor="-1" ver="-1"/>
</color>
</Background>
<Border/>
</Style>
<Style style_name="默认" full="true" border_source="-1" imageLayout="1">
<FRFont name="simhei" style="0" size="72"/>
<Background name="NullBackground"/>
<Border/>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72">
<foreground>
<FineColor color="-1" hor="-1" ver="-1"/>
</foreground>
</FRFont>
<Background name="ColorBackground">
<color>
<FineColor color="-12478742" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="0" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="2" imageLayout="1">
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="ColorBackground">
<color>
<FineColor color="-1" hor="-1" ver="-1"/>
</color>
</Background>
<Border>
<Top style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
<Style horizontal_alignment="4" imageLayout="1">
<Format class="com.fr.base.CoreDecimalFormat" roundingMode="6">
<![CDATA[#,##0.00]]></Format>
<FRFont name="微软雅黑" style="0" size="72"/>
<Background name="NullBackground"/>
<Border>
<Top style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Top>
<Bottom style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Bottom>
<Left style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Left>
<Right style="1">
<color>
<FineColor color="-4144960" hor="-1" ver="-1"/>
</color>
</Right>
</Border>
</Style>
</StyleList>
<DesensitizationList/>
<DesignerVersion DesignerVersion="LAA"/>
<PreviewType PreviewType="0"/>
<TemplateThemeAttrMark class="com.fr.base.iofile.attr.TemplateThemeAttrMark">
<TemplateThemeAttrMark name="兼容主题" dark="false"/>
</TemplateThemeAttrMark>
<StrategyConfigsAttr class="com.fr.esd.core.strategy.persistence.StrategyConfigsAttr">
<StrategyConfigs>
<StrategyConfig dsName="p_line_code" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="p_com1" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="p_com2" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds1_item" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="ds2_投行" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
<StrategyConfig dsName="数据(参数固化)" enabled="false" useGlobal="true" shouldMonitor="true" shouldEvolve="false" scheduleBySchema="false" timeToLive="1500000" timeToIdle="86400000" updateInterval="1500000" terminalTime="" updateSchema="0 0 8 * * ? *" activeInitiation="false"/>
</StrategyConfigs>
</StrategyConfigsAttr>
<TemplateIdAttMark class="com.fr.base.iofile.attr.TemplateIdAttrMark">
<TemplateIdAttMark TemplateId="ad2fc87c-7e94-4ba8-a7c3-29fa9b62b60d"/>
</TemplateIdAttMark>
<TemplateCloudInfoAttrMark class="com.fr.plugin.cloud.analytics.attr.TemplateInfoAttrMark" pluginID="com.fr.plugin.cloud.analytics.v11" plugin-version="3.18.0.20230712">
<TemplateCloudInfoAttrMark createTime="1714975977697"/>
</TemplateCloudInfoAttrMark>
</WorkBook>
