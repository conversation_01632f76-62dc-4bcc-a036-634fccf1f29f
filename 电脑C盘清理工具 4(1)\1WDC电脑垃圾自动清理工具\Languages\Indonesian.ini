; 
 
 ;     F e w   r u l e s   f o r   t r a n s l a t o r : 
 
 ;     F o r   b e g i n n i n g   o f   t r a n s l a t e   -   c o p y   e n g l i s h . i n i   i n t o   t h e   y o u r l a n g u a g e . i n i 
 
 ;     P l e a s e ,   t r a n s l a t e   t e x t   o n l y   a f t e r   s i m b o l   '   =   ' 
 
 ; 
 
 ; 
 
 ;   T r a n s l a t i o n A u t h o r :   I r s N a 
 
 ;   T r a n s l a t i o n A u t h o r E m a i l :   i r s n a l a b a r 4 2 9 @ g m a i l . c o m 
 
 ; 
 
 
 
 [ T r a n s l a t i o n ] 
 
 L a n g C o d e = 0 4 2 1 
 
 R e a d H a b i t = 
 
 L a n g u a g e N a m e = I N D 
 
 D e f a u l t F o n t = T a h o m a 
 
 D e f a u l t F o n t S i z e = 8 
 
 D e f a u l t C h a r s e t = A N S I _ C H A R S E T 
 
 I n f o r m a t i o n = K o n f i r m a s i 
 
 W a r n i n g = P e r i n g a t a n 
 
 E r r o r = K e s a l a h a n 
 
 C o n f i r m = K o n f i r m a s i 
 
 p m G o = B u k a   f o l d e r   t e r t e n t u 
 
 p m N o n e = T i d a k   m e m i l i h 
 
 p m A l l 1 = P i l i h   S e m u a 
 
 p m r e c = R e k o m e n d a s i 
 
 p m I g n o r e = A b a i k a n   i t e m   i n i 
 
 p m E x p o r t = E x p o r t   k e   . . . 
 
 p m I n v e r t = M e m b a l i k k a n   p i l i h a n 
 
 F r m W D C . p m P o r t a b l e . c a p t i o n = B u a t   v e r s i   p o r t a b l e 
 
 F r m W D C . p m R e s t o r e . c a p t i o n = K e m b a l i k a n 
 
 F r m W D C . p m S e t t i n g s . c a p t i o n = P e n g a t u r a n 
 
 F r m G e n e r a l . c h k u a c . c a p t i o n = L e w a t i   U A C 
 
 F r m W D C . p m H e l p . c a p t i o n = B a n t u a n   o n l i n e 
 
 F r m W D C . p m H o m e . c a p t i o n = B e r a n d a 
 
 F r m W D C . p m F o r u m . c a p t i o n = F o r u m 
 
 F r m W D C . p m C o n t a c t . c a p t i o n = H u b u n g i   k a m i 
 
 F r m W D C . p m C h e c k N e w . c a p t i o n = C e k   p e m b a r u a n 
 
 F r m W D C . p m L a n g . c a p t i o n = B a h a s a 
 
 F r m W D C . p m S k i n . c a p t i o n = G a n t i   k u l i t 
 
 F r m W D C . p m a b o u t . c a p t i o n = T e n t a n g 
 
 F r m W D C . b t n c o m m . c a p t i o n = P e m b e r s i h   B i a s a 
 
 F r m W D C . b t n a d v . c a p t i o n = P e m b e r s i h   L a n j u t a n 
 
 F r m W D C . b t n s i l m . c a p t i o n = R a m p i n g k a n   S i s t e m 
 
 F r m W D C . b t n l i k e i t . c a p t i o n = S u k a i 
 
 F r m W D C . b t n s u g g e s t . c a p t i o n = K r i t i k   d a n   s a r a n 
 
 F r m W D C . b t n u p d a t e . c a p t i o n = C e k   p e m b a r u a n 
 
 F r m W D C . b t n f a c e b o o k . h i n t = I k u t i   k a m i   d i   F a c e b o o k 
 
 F r m W D C . b t n t w i t t e r . h i n t = K i r i m   k e   T w i t t e r 
 
 F r m W D C . b t n e m a i l . h i n t = K i r i m   k e   t e m a n   a n d a 
 
 F r m W D C . b t n a s s i s t . c a p t i o n = B a n t u a n 
 
 F r m W D C . b t n m e s s a g e . c a p t i o n = P e s a n 
 
 F r m A d v S e t t i n g s . l b l i n f o . c a p t i o n = " T e m u k a n   f i l e   k o s o n g "   h a n y a   b e r l a k u   s e k a l i .   A n d a   h a r u s   m e m i l i h n y a   l a g i   l a i n   w a k t u . 
 
 F r m a d v d c . b t n s c a n . c a p t i o n = P i n d a i 
 
 F r m a d v d c . l b l p a t h . c a p t i o n = P i n d a i   L o k a s i : 
 
 F r m a d v d c . b t n r e s c a n . c a p t i o n = P i n d a i   l a g i 
 
 F r m a d v d c . b t n r e . c a p t i o n = R e k o m e n d a s i 
 
 F r m a d v d c . b t n a l l . c a p t i o n = P i l i h   S e m u a 
 
 F r m a d v d c . b t n a d v a n c e d . c a p t i o n = S e t e l a n   L a n j u t a n 
 
 F r m a d v d c . b t n n o n e . c a p t i o n = T i d a k   m e m i l i h 
 
 F r m a d v d c . l b l w r c i n f o . c a p t i o n = 
 
 F r m a d v d c . l b l w r c . c a p t i o n = 
 
 F r m a d v d c . l b l s p e e d . c a p t i o n = 
 
 F r m a d v d c . l b l r e c . c a p t i o n = A p l i k a s i   l a i n   y a n g   d i s u k a i   p e n g g u n a : 
 
 F r m a d v d c . v t f i l e s . C o l u m n s [ 0 ] = N a m a   F i l e 
 
 F r m a d v d c . v t f i l e s . C o l u m n s [ 1 ] = U k u r a n 
 
 F r m a d v d c . v t f i l e s . C o l u m n s [ 2 ] = 
 
 F r m a d v d c . v t f i l e s . C o l u m n s [ 3 ] = T e r a k h i r   D i a k s e s 
 
 F r m a d v d c . v t f i l e s . C o l u m n s [ 4 ] = T i p e 
 
 F r m A d v S e t t i n g s . b t n o k . c a p t i o n = O k 
 
 F r m A d v S e t t i n g s . b t n a d d . c a p t i o n = T a m b a h 
 
 F r m A d v S e t t i n g s . b t n r e m o v e . c a p t i o n = B u a n g 
 
 F r m A d v S e t t i n g s . b t n c a n c e l . c a p t i o n = B a t a l 
 
 F r m A d v S e t t i n g s . w c k n u l l . c a p t i o n = C a r i   f i l e   k o s o n g   ( 0   b y t e s ,   l e b i h   l a m b a t ) 
 
 F r m A d v S e t t i n g s . w c k s h o r t c u t . c a p t i o n = C a r i   p i n t a s a n   t i d a k   v a l i d   ( l e b i h   l a m b a t ) 
 
 F r m A d v S e t t i n g s . c a p t i o n = S e t e l a n   L a n j u t a n 
 
 F r m A d v S e t t i n g s . b t n r e . c a p t i o n = R e k o m e n d a s i 
 
 F r m A d v S e t t i n g s . b t n a l l . c a p t i o n = P i l i h   S e m u a 
 
 F r m A d v S e t t i n g s . b t n n o n e . c a p t i o n = T i d a k   m e m i l i h 
 
 F r m i n p u t . b t n o k . c a p t i o n = O k 
 
 F r m i n p u t . b t n c a n c e l . c a p t i o n = B a t a l 
 
 F r m A b o u t . c a p t i o n = T e n t a n g 
 
 F r m A b o u t . l b l v e r . c a p t i o n = V e r s i   S e k a r a n g : 
 
 F r m A b o u t . l b l h o m e . c a p t i o n = B e r a n d a : 
 
 F r m A b o u t . l b l e m a i l . c a p t i o n = E m a i l   b a n t u a n : 
 
 F r m A b o u t . l b l t h a n k s . c a p t i o n = T e r i m a   k a s i h   k e p a d a   o r a n g - o r a n g   b e r i k u t   u n t u k   k o n t r i b u s i   m e r e k a : 
 
 F r m A b o u t . b t n g o o g l e . h i n t = I k u t i   k a m i   d i   G o o g l e + 
 
 F r m A u t o r u n S e t t i n g s . l b l c m d . c a p t i o n = M o d e   B a r i s   P e r i n t a h 
 
 F r m A u t o r u n S e t t i n g s . l b l c m d i n f o . c a p t i o n = A n d a   d a p a t   m e n j a l a n k a n   p r o g r a m ,   m e m i n d a i     d a n     m e m b e r s i h k a n   d r i v e   a n d a   s e c a r a   o t o m a t i s   d a l a m   b a r i s   p e r i n t a h .   U n t u k   i n f o r m a s i   l e b i h   l a n j u t ,   t e k a n   F 1   a t a u   k l i k   U R L   b e r i k u t   u n t u k   m e m b a c a   d o k u m e n   b a n t u a n   k a m i . 
 
 F r m A u t o r u n S e t t i n g s . c h k 1 . c a p t i o n = B u a t   i k o n   ' B e r s i h   d a l a m   1 - k l i k '   d a n   t e m p a t k a n   d a l a m   d e k s t o p 
 
 F r m A u t o r u n S e t t i n g s . c h k a d v . c a p t i o n = T e r n a s u k   p e m b e r s i h   l a n j u t a n 
 
 F r m A u t o r u n S e t t i n g s . b t n h e l p . c a p t i o n = B a n t u a n   o n l i n e 
 
 F r m c o m m o n . l b l c u s t o m . c a p t i o n = K u s t o m 
 
 F r m c o m m o n . l b l c u s t o m h i n t . c a p t i o n = P i n d a i   e n t r i   k u s t o m . 
 
 F r m C o m m o n . b t n B a c k . c a p t i o n = K e m b a l i   k e   U t a m a 
 
 F r m C o m m o n . b t n a l l . c a p t i o n = P i l i h   S e m u a 
 
 F r m C o m m o n . b t n r e . c a p t i o n = R e k o m e n d a s i 
 
 F r m C o m m o n . b t n n o n e . c a p t i o n = T i d a k   m e m i l i h 
 
 F r m C o m m o n . w c k a d v . c a p t i o n = T e r n a s u k   p e m b e r s i h   l a n j u t a n 
 
 F r m C o m m o n . W i s e L a b e l 1 . c a p t i o n = P e n g i n g a t 
 
 F r m C o m m o n . l b l t y p e . c a p t i o n = P i l i h   t i p e   m e n j a l a n k a n : 
 
 F r m C o m m o n . l b l w e e k . c a p t i o n = P i l i h   h a r i : 
 
 F r m C o m m o n . l b l t i m e . c a p t i o n = W a k t u : 
 
 F r m C o m m o n . b t n S c a n . c a p t i o n = P i n d a i 
 
 F r m C o m m o n . l b l s t a t . c a p t i o n = C e p a t   d a n   m u d a h   u n t u k   m e m b e r s i h k a n   j e j a k   s e r t a   f i l e   s a m p a h   . 
 
 F r m C o m m o n . b t n c a n c e l . c a p t i o n = B a t a l 
 
 F r m C o m m o n . b t n r e s c a n . c a p t i o n = P i n d a i   l a g i 
 
 F r m C o m m o n . l b l t r a s h . c a p t i o n = S a m p a h 
 
 F r m C o m m o n . l b l t r a c e . c a p t i o n = R i w a y a t 
 
 F r m C o m m o n . l b l a p p . c a p t i o n = A p l i k a s i 
 
 F r m C o m m o n . l b l c o o k i e . c a p t i o n = C o o k i e 
 
 F r m C o m m o n . l b l t r a s h h i n t . c a p t i o n = F i l e   s a m p a h   d i   k o m p u t e r . 
 
 F r m C o m m o n . l b l t r a c e h i n t . c a p t i o n = J e j a k   s i s t e m   d a n   p e n g g u n a . 
 
 F r m C o m m o n . l b l a p p h i n t . c a p t i o n = F i l e   s a m p a h   a t a u   j e j a k   y a n g   d i b u a t   o l e h   a p l i k a s i   l a i n . 
 
 F r m C o m m o n . l b l c o o k i e h i n t . c a p t i o n = C o o k i e   p e r a m b a n   d a n   f l a s h . 
 
 F r m E x c l u s i o n . b t n A d d F i l e . c a p t i o n = T a m b a h   f i l e 
 
 F r m E x c l u s i o n . b t n A d d F o l d e r . c a p t i o n = T a m b a h   f o l d e r 
 
 F r m E x c l u s i o n . b t n A d d T y p e . c a p t i o n = T a m b a h   t i p e   f i l e 
 
 F r m E x c l u s i o n . B t n A d d C o o k i e . c a p t i o n = T a m b a h   c o o k i e 
 
 F r m E x c l u s i o n . b t n I m p o r t . c a p t i o n = I m p o r t 
 
 F r m E x c l u s i o n . b t n E x p o r t . c a p t i o n = E x p o r t 
 
 F r m E x c l u s i o n . b t n R e m o v e . c a p t i o n = B u a n g 
 
 F r m G e n e r a l . l b t r a n . c a p t i o n = T e r j e m a h k a n   k e . . . 
 
 F r m G e n e r a l . c h k N e w s . c a p t i o n = O t o m a t i s   d a p a t k a n   b e r i t a 
 
 F r m G e n e r a l . c h k n o t i c e . c a p t i o n = T a m p i l k a n   p e m b e r i t a h u a n   p e m b e r s i h a n   o t o m a t i s 
 
 F r m G e n e r a l . c h k w i p e . c a p t i o n = P e n g h a p u s a n   a m a n   ( L e b i h   l a m b a t ) 
 
 F r m G e n e r a l . W i s e L a b e l 1 . c a p t i o n = C e k   p e m b a r u a n : 
 
 F r m G e n e r a l . W i s e L a b e l 2 . c a p t i o n = B a h a s a   U t a m a : 
 
 F r m G e n e r a l . b t n c h e c k n e w . c a p t i o n = C e k   s e k a r a n g 
 
 F r m G e n e r a l . c h k e x p . c a p t i o n = G a b u n g   d a l a m   P r o g r a m   P e n i n g k a t a n   P e n g a l a m a n   P e n g g u n a 
 
 F r m G e n e r a l . l b l e x p . c a p t i o n = D e t a i l 
 
 F r m G e n e r a l . c h k a s s i s t . c a p t i o n = B e r i t a h u   s a y a   s a a t   s e s e o r a n g   m e m p o s t i n g   p e r t a n y a a n   b e r b a y a r . 
 
 F r m G e n e r a l . c k R e s t o r e P o i n t . c a p t i o n = B u a t   t i t i k   p e m u l i h a n   s i s t e m   s e b e l u m   m e r a m p i n g k a n   s i s t e m 
 
 F r m C u s t o m . W i s e L a b e l 4 . c a p t i o n = F o l d e r   k h u s u s   u n t u k   d i k o s o n g k a n   ( B e r l a k u   p a d a   p e m b e r s i h   l a n j u t a n ) 
 
 F r m C u s t o m . b t n A d d . c a p t i o n = T a m b a h 
 
 F r m C u s t o m . b t n R e m o v e . c a p t i o n = B u a n g 
 
 F r m C u s t o m . l b l C u s t o m . c a p t i o n = S e s u a i k a n   a t u r a n   p e m i n d a i a n   a n d a . 
 
 F r m C u s t o m . l b l c u s t o m i n f o . c a p t i o n = J i k a   a n d a   p e n g g u n a   b e r p e n g a l a m a n ,   a n d a   d a p a t   m e n y e s u a i k a n   a t u r a n   p e m i n d a i a n   u n t u k   m e m i n d a i   l e b i h   b a n y a k   f i l e   t i d a k   b e r g u n a .   K l i k   l i n k   b e r i k u t   u n t u k   m e n g e t a h u i   c a r a   m e m b u a t   k o n f i g u r a s i   f i l e   a n d a . 
 
 F r m C u s t o m . b t n h e l p . c a p t i o n = B a n t u a n   o n l i n e 
 
 F r m R e s t o r e . b t n r e s t o r e . c a p t i o n = K e m b a l i k a n 
 
 F r m R e s t o r e . b t n r e m o v e . c a p t i o n = B u a n g 
 
 F r m R e s t o r e . B t n c l o s e . c a p t i o n = T u t u p 
 
 F r m R e s t o r e . c a p t i o n = K e m b a l i k a n 
 
 F r m S e t t i n g s . t s C o m m o n . c a p t i o n = U m u m 
 
 F r m S e t t i n g s . t s A u t o R u n . c a p t i o n = J a l a n k a n   O t o m a t i s 
 
 F r m S e t t i n g s . t s E x c l u s i o n . c a p t i o n = P e n g e c u a l i a n 
 
 F r m S e t t i n g s . b t n A b o u t . c a p t i o n = T e n t a n g 
 
 F r m S e t t i n g s . b t n o k . c a p t i o n = O k 
 
 F r m S e t t i n g s . b t n c a n c e l . c a p t i o n = B a t a l 
 
 F r m S e t t i n g s . c a p t i o n = P e n g a t u r a n 
 
 F r m S e t t i n g s . t s C u s t o m . c a p t i o n = K u s t o m 
 
 F r m S y s C l e a n . l b l r e c o m m e n d i n f o . c a p t i o n = P l e a s e   t r y   o u r   s m a r t   p r o d u c t s   t o   i m p r o v e   W i n d o w s   p e r f o r m a n c e   n o w . 
 
 F r m S y s C l e a n . l b l c a r e i n f o . c a p t i o n = W i s e   C a r e   3 6 5   i s   a n   a l l - i n - o n e   P C   t u n e - u p   u t i l i t e s ,   i n c l u d i n g   a l l   f u n c t i o n s   o f   W i s e   R e g i s t r y   C l e a n e r   a n d   W i s e   D i s k   C l e a n e r .   E a s y   t o   u s e   a n d   E f f e c t i v e ! 
 
 F r m S y s C l e a n . l b l r e . c a p t i o n = W i s e   C a r e   3 6 5   i s   a n   a l l - i n - o n e   P C   t u n e - u p   u t i l i t e s ,   i n c l u d i n g   a l l   f u n c t i o n s   o f   W i s e   R e g i s t r y   C l e a n e r   a n d   W i s e   D i s k   C l e a n e r .   E a s y   t o   u s e   a n d   E f f e c t i v e ! 
 
 F r m S y s C l e a n . b t n S l i m m i n g . c a p t i o n = B u a n g 
 
 F r m S y s C l e a n . b t n a l l . c a p t i o n = P i l i h   S e m u a 
 
 F r m S y s C l e a n . b t n r e c o m m e n d e d . c a p t i o n = R e k o m e n d a s i 
 
 F r m S y s C l e a n . b t n n o n e . c a p t i o n = T i d a k   m e m i l i h 
 
 F r m S y s C l e a n . v t s y s . C o l u m n s [ 0 ] = I t e m 
 
 F r m S y s C l e a n . v t s y s . C o l u m n s [ 1 ] = U k u r a n 
 
 F r m S y s C l e a n . v t s y s . C o l u m n s [ 2 ] = L o k a s i 
 
 F r m S y s C l e a n . v t s y s . C o l u m n s [ 3 ] = S a r a n 
 
 F r m D i s k D e f r a g . b t n f r e e . c a p t i o n = R u a n g   b e b a s 
 
 F r m D i s k D e f r a g . b t n f o l d e r . c a p t i o n = F o l d e r 
 
 F r m D i s k D e f r a g . b t n f i l e . c a p t i o n = F i l e 
 
 F r m D i s k D e f r a g . b t n s y s t e m . c a p t i o n = F i l e   s i s t e m 
 
 F r m D i s k D e f r a g . b t n F r a g m e n t e d . c a p t i o n = F r a g m e n 
 
 F r m D i s k D e f r a g . b t n m f t . c a p t i o n = M F T 
 
 F r m D i s k D e f r a g . c h k s h u t d o w n . c a p t i o n = M a t i k a n   s e t e l a h   d i s k   d e f r a g . 
 
 F r m D i s k D e f r a g . b t n p a u s e . c a p t i o n = J e d a 
 
 F r m D i s k D e f r a g . b t n s t o p . c a p t i o n = H e n t i k a n 
 
 F r m D i s k D e f r a g . b t n D e f r a g . c a p t i o n = D e f r a g m e n t 
 
 F r m W D C . b t n d e f r a g . c a p t i o n = D i s k   D e f r a g 
 
 F r m S c h e d u l e r . c a p t i o n = P e n g i n g a t 
 
 [ L a n g ] 
 
 N a t i v e L a n g u a g e = I n d o n e s i a n 
 
 [ G U I ] 
 
 B u t t o n Y e s = Y a 
 
 B u t t o n N o = T i d a k 
 
 B u t t o n O K = O k 
 
 B u t t o n M i n i m i z e = K e c i l k a n 
 
 B u t t o n M a x i m i z e = B e s a r k a n 
 
 B u t t o n C l o s e = T u t u p 
 
 B u t t o n R e s t o r e = K e m b a l i k a n 
 
 B u t t o n M e n u = M e n u 
 
 [ M e s s a g e ] 
 
 N o N e w s = A n d a   t i d a k   m e m p u n y a i   p e s a n   y a n g   b e l u m   t e r b a c a . 
 
 C o n f i r m R e m o v e D o w n l o a d = T i n d a k a n   i n i   d a p a t   m e n g h a p u s   s e m u a   f i l e   d i   f o l d e r   u n d u h a n   ,   a p a k a h   a n d a   y a k i n   u n t u k   m e l a n j u t k a n ? 
 
 S k i p p e d = D i l e w a t i 
 
 N e e d A d m i n = F i t u r   i n i   t e r s e d i a   u n t u k   a d m i n i s t r a t o r .   J i k a   a n d a   i n g i n   m e n j a l a n k a n   f i t u r   i n i ,   s i l a k a n   m a s u k   s e b a g a i   a d m i n i s t r a t o r . 
 
 E n t r i e s = E n t r i 
 
 C l e a n = B e r s i h k a n 
 
 S c h e d u l e H i n t = M e m b e r s i h k a n   k o m p u t e r   a n d a   s e c a r a   t e r a t u r . 
 
 U p d a t e E r r o r = U p d a t e   e r r o r ,   a p a k a h   a n d a   i n g i n   m e n g u n d u h   v e r s i   t e r b a r u   d i   w e b s i t e   k a m i ? 
 
 L e s t O n e I t e m = A n d a   h a r u s   m e m l i h   s e t i d a k n y a   s a t u   i t e m   u n t u k   d i b e r s i h k a n . 
 
 H i s t o r y S a v e d = T o t a l   r u a n g   y a n g   d i s i m p a n :   % s 
 
 D i s a b l e A d d = D e m i   k e a m a n a n ,   b e b e r a p a   f o l d e r   k h u s u s   ( s e p e r t i   " W i n d o w s   F o l d e r " ,   " B o o t   o f   H a r d   D r i v e "   a n d   " C D - R O M " )   t i d a k   d a p a t   d i t a m b a h k a n   k e   d a l a m   l i s t   i n i . 
 
 C o n f i r m A d d = A p a k a h   a n d a   y a k i n   u n t u k   m e n g h a p u s   s e m u a   f i l e   d a l a m   % s   s a a t   a n d a   m e n j a l a n k a n   " P e m b e r s i h   l a n j u t a n " ? 
 
 D u p F o l d e r = F o l d e r   i n i   s u d a h   d i t a m b a h k a n   d a l a m   l i s t . 
 
 E x I m p o r t = L i s t   p e n g e c u a l i a n   t e l a h   d i   e x p o r t   k e : 
 
 S e l e c t H a r d D r i v e = S i l a k a n   p i l i h   h a r d   d r i v e 
 
 C r e a t i o n D a t e = T a n g g a l   D i b u a t 
 
 D e s c r i p t i o n = D e s k r i p s i 
 
 N o w C l e a n i n g = S e d a n g   m e m b e r s i i h k a n : 
 
 S c h e d u l e N e x t T i m e = W a k t u   b e r i k u t n y a : 
 
 N e v e r = S e l a m a n y a 
 
 I n p u t T y p e = M a s u k k a n   n a m a   f i l e   a t a u   t i p e   f i l e   ( C o n t o h :   * . t m p   d l l . . . ) 
 
 W a r n N o T y p e = S i l a k a n   p i l i h   s e t i d a k n y a   s a t u   f i l e   u n t u k   d i c a r i ! 
 
 N o I t e m C h e c k e d = S i l a k a n   p i l i h   s a t u   a t a u   l e b i h   f i l e   u n t u k   d i b e r s i h k a n . 
 
 W D C R u n n i n g = P r o g r a m   s e d a n g   b e r j a l a n . 
 
 R e m o v e S y s R e s t o r e = A p a k a h   a n d a   i n g i n   m e n g h a p u s   t i t i k   p e m u l i h a n   s i s t e m   y a n g   d i p i l i h ? 
 
 R e s t o r e F r o m S y s R e s t o r e = A n d a   h a r u s   m e r e s t a r t   k o m p u t e r   a n d a   u n t u k   p e m u l i h a n   s i s t e m .   A p a k a h   a n d a   i n g i n   m e m u l a i   p e m u l i h a n   s e k a r a n g ? 
 
 W a r n Z e r o F i l e = A n d a   m e m i l i h   m o d e   " f i l e   k o s o n g " .   D i p e r l u k a n   b e b e r a p a   f i l e   0   b y t e ,   s i l a k a n   t i n j a u   s e b e l u m   a n d a   h a p u s .   A p a k a h   a n d a   y a k i n   u n t u k   m e l a k u k a n   i n i ? 
 
 S y s R e s t o r i n g = B e r s i a p   u n t u k   m e m u l i h k a n   s i s t e m . . . 
 
 S y s C r e a t i n g = M e m b u a t   T i t i k   P e m u l i h a n   s i s t e m . . . 
 
 B r o w s e = T e l u s u r i . . . 
 
 A l l H a r d D r i v e = H a r d   d r i v e   l o k a l 
 
 C l e a n i n g = M e m b e r s i h k a n . . . . 
 
 E x p o r t e d = I t e m   s a a t   i n i   t e l a h   d i   e x p o r t   k e   % s ,   a p a k a h   a n d a   i n g i n   m e m b u k a   f i l e   s e k a r a n g ? 
 
 L e f t T i m e S e c = D u r a s i :   % s   d e t i k ( s ) 
 
 S a f e t o d e l = I t e m   i n i   d a p a t   d i h a p u s   d e n g a n   a m a n . 
 
 M o r e S p a c e = U n t u k   p e n y i m p a n a n   l e b i h . 
 
 U n k n o w = T i d a k   d i k e t a h u i . 
 
 A d d F i l e T y p e = T a m b a h   t i p e   f i l e 
 
 A l r e a d E x i s t = % s   t e l a h   b e r h a s i l   d i t a m b a h k a n   d a l a m   l i s t . 
 
 i l l e g a l T y p e = % s   a d a l a h   f i l e   i l e g a l . 
 
 S u m m a r y = R i n g k a s a n 
 
 I n p u t S u m m a r y = S i l a k a n   m a s u k k a n   d e s k r i p s i   u n t u k   t i p e   i n i : 
 
 n o s e l e c t e d = T i d a k   a d a   i t e m   y a n g   d i p i l i h . 
 
 D e l F i l e T y p e = A p a k a h   a n d a   y a k i n   u n t u k   m e n g h a p u s   t i p e   f i l e   i n i ? 
 
 S c a n A b o r t e d = P e m i n d a i a n   t e l a h   d i b a t a l k a n ! 
 
 T r a s h F o u n d = % d   f i l e ,   t o t a l   d a r i   % s ,   t e l a h   d i t e m u k a n . 
 
 N o T r a s h A n d T r a c e = S e l a m a t !   A n d a   m e m l i k i   k o m p u t e r   y a n g   b e r s i h ! 
 
 C l e a n D o n e = P e m b e r s i h a n   t e l a h   s e l e s a i ! 
 
 L e s t O n e = M o h o n   p i l i h   s e t i d a k n y a   s a t u   i t e m   u n t u k   d i p i n d a i . 
 
 L e s t O n e C l e a n = M o h o n   p i l i h   s e t i d a k n y a   s a t u   i t e m   u n t u k   d i b e r s i h k a n . 
 
 S c a n n i n g = M e m i n d a i . . . 
 
 I n v a l i d I g n o r I t e m = T i d a k   d a p a t   m e n g a b a i k a n   i t e m   i n i . 
 
 T r a c e F o u n d = % d   j e j a k   t e l a h   d i t e m u k a n . 
 
 C l e a n N o w = D i s a r a n k a n   p e m b e r s i h a n   s e k a r a n g . 
 
 T r a s h R e m o v e d = % d   f i l e   s u d a h   d i h a p u s   d a n   % d   k a p a s i t a s   r u a n g   p e n y i m p a n a n   b e r t a m b a h . 
 
 T r a c e R e m o v e d = % d   j e j a k   t e l a h   d i h a p u s . 
 
 L a s t C o m m o n C l e a n = T e r a k h i r   k a l i   p e n y i m p a n a n   a n d a   d i b e r s i h k a n : 
 
 W o r k i n g = M o h o n   t u n g g u   t u g a s   s a a t   i n i   s e l e s a i ,   d a n   c o b a   l a g i . 
 
 N o F i l e s F o u n d = T i d a k   a d a   f i l e   t e m p 
 
 N o T r a c e s F o u n d = T i d a k   a d a   j e j a k 
 
 F i n d T r a c e s = % d   j e j a k 
 
 N o I t e m F o u n d = T i d a k   d i t e m u k a n   i t e m 
 
 F i n d F i l e s S i z e = % d   f i l e ,   t o t a l   d a r i   % s ,   d i t e m u k a n 
 
 F i n d T r a c e s S e c t i o n = % d   j e j a k   d i t e m u k a n   p a d a   b a g i a n   i n i 
 
 C a n F r e e S p a c e = % d   f i l e   d i t e m u k a n   d a n   % s   d i s i m p a n 
 
 T o t a l T r a c e s = T o t a l   d a r i   % d   j e j a k   d i t e m u k a n 
 
 E n a b l e R e s t o r e = T i t i k   p e m u l i h a n   d i n o n a k t i f k a n .   A p a k a h   a n d a   i n g i n   m e n g a k t i f k a n n y a ? 
 
 R e s t o r e P o i n t F a i l = G a g a l   d a l a m   m e m b u a t   t i t i k   p e m u l i h a n . 
 
 E r r o r M s g = P e s a n   k e s a l a h a n : 
 
 N o N e w V e r = S e l a m a t !   A n d a   m e n g g u n a k a n   v e r s i   t e r b a r u ! 
 
 K e y W o r d = S i l a k a n   m a s u k k a n   k a t a   k u n c i   a n d a : 
 
 A d d F o l d e r = T a m b a h   f o l d e r 
 
 C a n n o t C r T a s k = T i d a k   d a p a t   m e m b u a t   j a d w a l   t u g a s . 
 
 I n v a l i d T y p e = M o h o n   i n p u t   e k s t e n s i   f i l e   y a n g   v a l i d . 
 
 N o T y p e = M o h o n   c e n t a n g   s e t i d a k n y a   s a t u   t i p e   u n t u k   d i c a r i . 
 
 F a i l T o D e l e t e = B e b e r a p a   f i l e   h a n y a   d a p a t   d i h a p u s   s e t e l a h   r e b o o t . 
 
 G o o d H a b i t = M e n j a g a   P C   a n d a   t e t a p   b e r s i h   a d a l a h   k e b i a s a a n   b a i k .   T e t a p   p e r t a h a n k a n ! 
 
 7 A f t e r = % d   h a r i   b e r l a l u   s e j a k   p e m b e r s i h a n   t e r a k h i r .   D i s a r a n k a n   u n t u k   p i n d a i   s e g e r a . 
 
 7 B e f o r e = M e r u p a k a n   k e b i a s a a n   b a i k   u n t u k   m e n j a g a   d r i v e   a n d a   t e t a p   b e r s i h   d a n   p r i v a s i   a n d a   t e r j a m i n . 
 
 T a s k E x i s t s = J a d w a l   t u g a s   s u d a h   d i p a k a i .   A p a k a h   a n d a   i n g i n   m e n g h a p u s n y a   a t a u   m e m b u a t   b a r u ?   ( T i d a k   -   E d i t   t u g a s   y a n g   d i p a k a i .   B a t a l   -   B a t a l k a n   p r o s e s . ) 
 
 N o B a c k u p S e l e c t e d = S i l a k a n   p i l i h   s e t i d a k n y a   s a l a h   s a t u   c a d a n g a n   u n t u k   d i p u l i h k a n . 
 
 S t a r t S c a n = P i n d a i 
 
 S t a r t C l e a n = B e r s i h k a n 
 
 C a n c e l = B a t a l 
 
 I d l e = S a a t   d i a m 
 
 D a i l y = T i a p   h a r i 
 
 W e e k l y = T i a p   m i n g g u 
 
 M o n t h l y = T i a p   b u l a n 
 
 M o n d a y = S e n i n 
 
 T u e s d a y = S e l a s a 
 
 W e d n e s d a y = R a b u 
 
 T h u r s d a y = K a m i s 
 
 F r i d a y = J u m a t 
 
 S a t u r d a y = S a b t u 
 
 S u n d a y = M i n g g u 
 
 A v d D e f a u l t H i n t = B a g i a n   i n i   m e n y e d i a k a n   s e t e l a n   l a n j u t a n   u n t u k   m e n e m u k a n   l e b i h   b a n y a k   f i l e   s a m p a h   d a n   m e m b e b a s k a n   l e b i h   b a n y a k   r u a n g . 
 
 T r a s h F o u n d E x = F i l e   t e m p   b e r i k u t   y a n g   b e r h a s i l   d i t e m u k a n : 
 
 N o D r i v e C h e c k e d = A n d a   h a r u s   m e m i l i h   s a t u   a t a u   l e b i h   p e r a n g k a t     u n t u k   d p i n d a i . 
 
 S c a n F o r = M e m i n d a i   p e r a n g k a t : 
 
 F i n d D r i v e s = M e n c a r i   p e r a n g k a t . . . 
 
 L o g i n = S a a t   m a s u k 
 
 N e w V e r = V e r s i   t e r b a r u   W i s e   D i s k   C l e a n e r   t e l a h   t e r s e d i a !   A p a k a h   a n d a   i n g i n   m e n i n g k a t k a n   s e k a r a n g ? 
 
 T r a n s l a t e T o = T e r j e m a h k a n   k e . . . 
 
 A d d C o o k i e s = T a m b a h   c o o k i e 
 
 I n p u t C o o k i e = M a s u k k a n   C o o k i e   ( C o n t o h :   w i s e c l e a n e r . c o m   e t c . . . ) 
 
 I n v a l i d C o o k i e = C o o k i e   t i d a k   v a l i d 
 
 B r o w s e r R u n n i n g = % s   s e d a n g   b e r j a l a n .   A k a n k a h   A n d a   i n g i n   m e n u t u p n y a ?   S e b a l i k n y a ,   c a c h e   d a n   c o o k i e   t i d a k   d a p a t   d i b e r s i h k a n . 
 
 C o o k i e C o n f i r m = I t e m   t e r p i l i h   t e r m a s u k   " C o o k i e " .   A p a k a h   a n d a   y a k i n   u n t u k   m e n g h a p u s n y a ? 
 
 P W P C o n f i r m = I t e m   t e r p i l i h   t e r m a s u k   " B r o w s e r   S a v e d   P a s s w o r d " .   A p a k a h   a n d a   y a k i n   u n t u k   m e n g h a p u s n y a ? 
 
 P W C o o k i e C o n f i r m = I t e m   t e r p i l i h   t e r m a s u k   "   B r o w s e r   S a v e d   P a s s w o r d   d a n   C o o k i e " /     A p a k a h   a n d a   y a k i n   u n t u k   m e n g h a p u s n y a ? 
 
 [ C o m m o n C l e a n ] 
 
 S c a n A b o r t e d H i n t = K a m i   s a r a n k a n   a n d a   s e g e r a   m e m i n d a i   l a g i   u n t u k   m e n e m u k a n   j e j a k   d a n   f i l e   s a m p a h       . 
 
 D e f a u l t H i n t = M e m b e r s i h k a n   f i l e   s a m p a h   d a n   j e j a k   i n t e r n e t   d a p a t   m e n g h e m a t   r u a n g   a n d a   s e r t a   m e n g a m a n k a n   k o m p u t e r   a n d a . 
 
 S c a n A b o r t e d H i n t 1 = K a m i   m e n y a r a n k a n   s e g e r a   m e m i n d a i   l a g i   u n t u k   m e n e m u k a n   f i l e   s a m p a h . 
 
 W h e n I d l e = S a a t   d i a m 
 
 W h e n L o g i n = S a a t   m a s u k 
 
 [ S y s C l e a n ] 
 
 O p e n = B u k a 
 
 R e m o v e = B u a n g 
 
 M o r e S p a c e = O p s i o n a l 
 
 C l e a n i n g = M e m b e r s i h k a n . . . . 
 
 S c a n n i n g = M e m i n d a i . . . 
 
 S c a n E n d = T o t a l   d a r i   % d   i t e m   d i t e m u k a n .   % s   r u a n g   p e n y i m p a n a n   a k a n   t e r b e b a s   s e t e l a h   d i r a m p i n g k a n . 
 
 N o I t e m s = S e l a m a t !   P e r a n g k a t   s i s t e m   a n d a   s u d a h   b e r s i h . 
 
 S p a c e F r e e d = P e r a m p i n g a n   s e l e s a i .   % d   r u a n g   p e n y i m p a n a n   t e r b e b a s . 
 
 I n s t a l l e r = W i n d o w s   i n s t a l l e r   u s a n g 
 
 L o n g I n s t a l l e r = W i n d o w s   I n s t a l l e r   u s a n g   a d a l a h   f i l e   s a m p a h .   M e r e k a   d a p a t   d i h a p u s   u n t u k   m e m b e b a s k a n   r u a n g   p e n y i m p a n a n 
 
 P a t c h U n i n s t a l l = M e n c o p o t   f i l e   p a t h   W i n d o w s   U p d a t e 
 
 L o n g P a t c h U n i n s t a l l = J i k a   a n d a   t i d a k   b u t u h   m e n c o p o t   W i n d o w s   U p d a t e ,   a n d a   d a p a t   m e n g h a p u s   f i l e   t e r s e b u t . 
 
 D o w n l o a d e d f i l e s = 
 
 L o n g d o w n l o a d e d f i l e s = S e t e l a h   p e m a s a n g a n   p a t h   w i n d o w s   u p d a t e   b e r h a s i l ,   f i l e   t e r s e b u t   a m a n   u n t u k   d i h a p u s . 
 
 B a s e l i n e c a c h e = 
 
 L o n g B a s e l i n e c a c h e = F i l e   t e r s e b u t   d i b u a t   o l e h   s e r v i s   w i n d o w s   i n s t a l l e r   d a n   m e r e k a   d a p a t   d i h a p u s . 
 
 J P I M E = 
 
 L o n g J P I M E = J i k a   a n d a   t i d a k   b u t u h   f i l e   J a p a n e s e   I M E   l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 K o r e a n I M E = 
 
 L o n g K o r e a n I M E = J i k a   a n d a   t i d a k   b u t u h   f i l e   K o r e a n   I M E   l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 T C h i n e s e I M E = 
 
 L o n g T C h i n e s e I M E = J i k a   a n d a   t i d a k   b u t u h   f i l e   T r a d i t i o n a l   C h i n e s e   I M E   l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 M P i n y i n I M E = 
 
 L o n g M P i n y i n I M E = J i k a   a n d a   t i d a k   b u t u h   f i l e   M S   P i n y i n   I M E   l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 H e l p F i l e = F i l e   B a n t u a n   W i n d o w s 
 
 L o n g H e l p F i l e = J i k a   a n d a   t i d a k   b u t u h   f i l e   B a n t u a n   W i n d o w s     l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 S a m p l e P i c = 
 
 L o n g S a m p l e P i c = J i k a   a n d a   t i d a k   b u t u h   s a m p l e   p i c t u r e   o f   w i n d o w s     l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 S a m p l e M i c = 
 
 L o n g S a m p l e M i c = J i k a   a n d a   t i d a k   b u t u h   s a m p l e   m u s i c   o f   w i n d o w s     l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 S a m p l e V i d e o s = 
 
 L o n g S a m p l e V i d e o s = J i k a   a n d a   t i d a k   b u t u h   s a m p l e   v i d e o s   o f   w i n d o w s     l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 S a m p l e M e d i a = 
 
 L o n g S a m p l e M e d i a = J i k a   a n d a   t i d a k   b u t u h   s a m p l e   m e d i a   o f   w i n d o w s     l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 W a l l p a p e r = 
 
 L o n g W a l l p a p e r = J i k a   a n d a   t i d a k   b u t u h   w a l l p a p e r   f i l e s     o f   W i n d o w s     l a g i ,   a n d a   d a p a t   m e n g h a p u s n y a . 
 
 [ D i s k D e f r a g ] 
 
 A b o r t e d = G a g a l k a n 
 
 V o l u m e = D r i v e 
 
 F o r m a t = F i l e   s i s t e m 
 
 T o t a l S p a c e = T o t a l   r u a n g 
 
 F r e e S p a c e = R u a n g   b e b a s 
 
 F r a g m e n t e d = F r a g m e n 
 
 S t a t u s = S t a t u s 
 
 P m D e f r a g = D e f r a g m e n t 
 
 Q u i c k O p t = O p t i m a s i   C e p a t 
 
 F u l l O p t = O p t i m a s i   F u l l 
 
 P m A n l y s e = A n a l i s i s 
 
 R e s u m e = L a n j u t k a n 
 
 A n a l y i n g = M e n g a n a l i s a 
 
 D e f r a g m e n t i n g = M e n d e f r a g 
 
 O p t i m i z i n g = M e n g o p t i m a s i 
 
 A n a l y e d = D i a n a l i s i s 
 
 D e f r a g m e n t e d = D i d e f r a g 
 
 O p t i m i z e d = D i o p t i m a s i 
 
 P a u s e = J e d a 
 
 I s S S D = P i l i h a n   a n d a   t e r p a u t   d e n g a n   s a l a h   s a t u   S o l i d   S t a t e   D r i v e .   D a n   d i s k   d e f r a g   d a p a t   m e n g u r a n g i   m a s a   p a k a i .   A p a k a h   a n d a   y a k i n   u n t u k   t e t a p   m e l a n j u t k a n ? 
 
 F r a g m e n t e d R a t e = F r a g m e n t a s i 
 
 W i n 1 0 U p g r a d e = F i l e   u p g r a d e   W i n d o w s   1 0 
 
 W i n 1 0 U p g r a d e L o n g = J i k a   a n d a   s u d a h   m e n g u p g r a d e   k e   W i n d o w s   1 0 ,   m o h o n   h a p u s   f o l d e r   u n u k   m e n d a p a t k a n   l e b i h   b a n y a k   r u a n g . 
 
 [ A r e a ] 
 
 W i n I n s t a l l e r P a c k a g e = W i n d o w s   i n s t a l l e r   u s a n g 
 
 W i n d o w s P a i n t = 
 
 A d o b e F l a s h = 
 
 W i n d o w s S y s C a c h e = C a c h e   S i s t e m   W i n d o w s 
 
 W i n d o w s W o r d p a d = 
 
 W i n I n s t a l l e r C a c h e = 
 
 W i n S x s B a c k u p = 
 
 C o o k i e s = C o o k i e 
 
 D O M S t o r e C o o k i e s = 
 
 D o w n l o a d e d P a t c h = F i l e   U p d a t e   W i n d o w s 
 
 D o w n l o a d s = F i l e   U n d u h a n   P e r a m b a n r 
 
 D u m p = F i l e   D u m p   m e m o r y 
 
 E r r o r R e p o r t = F i l e   L a p o r a n   K e s a l a h a n 
 
 E x p l o r e r M R U = 
 
 F o n t C a c h e = 
 
 I I S L o g = 
 
 I n v a l i d L n k = P i n t a s a n   t i d a k   v a l i d 
 
 I n v a l i d S c T a s k = T u g a s   T e r j a d w a l   T i d a k   V a l i d 
 
 I n v a l i d S t a r t M e n u = M e n u   M u l a i   T I d a k   V a l i d 
 
 J u m p L i s t = 
 
 L o g F i l e = F i l e   L o g   S i s t e m 
 
 M E S C a c h e = 
 
 M e t r o C a c h e = 
 
 M S O f f i c e = 
 
 N e t w o r k m a p p i n g = 
 
 O f f i c e = 
 
 O f f i c e P i c t u r e = 
 
 O f f i c e R e c e n t = 
 
 P r e f e t c h = D a t a   P r e f e t c h   L a m a 
 
 R e c e n t d o c u m e n t s = D o k u m e n   T e r a k h i r   D I b u k a 
 
 R e c y c l e b i n = T e m p a t   S a m p a h   K o s o n g 
 
 R e m o t e D e s k t o p = F i l e   C a c h e   R e m o t e   D e s k t o p 
 
 R u n S t a r t M e n u = J a l a n k a n   d i   S t a r t   M e n u 
 
 S a f a r i C a c h e = 
 
 S a f a r i C o o k i e s = C o o k i e   S a f a r i 
 
 S a f a r i H i s t o r y = R i w a y a t   S a f a r i 
 
 S a f a r i P a s s w o r d s = F o r m   I n f o r m a s i   T e r s i m p a n   S a f a r i 
 
 S e a r c h L i s t = R i w a y a t   P e n c a r i a n   W i n d o w s 
 
 S y s T e m p = F i l e   T e m p   D i b u a t   O l e h   W i n d o w s 
 
 S y s t e m T r a c e = J e j a k   W i n d o w s   d a n   A p l i k a s i 
 
 T h u m b C a c h e = C a c h e   G a m b a r   K e c i l 
 
 T r a c e C l e a n = J e j a k   d i   K o m p u t e r 
 
 T r a r y N o t i f y = C a c h e   I k o n   A r e a   N o t i f i k a s i 
 
 T r a s h C l e a n = F i l e   d i   K o m p u t e r 
 
 T y p e I n t e r n e t C a c h e = C a c h e   P e r a m b a n   W e b 
 
 T y p e I n t e r n e t T r a c e = J e j a k   P e r a m b a n   W e b 
 
 T y p e M e d i a = 
 
 T y p e O t h e r A p p = A p l i k a s i   L a i n 
 
 T y p e S y s t e m = S i s t e m   W i n d o w s 
 
 T y p e W C o m p o n e n t s = K o m p o n e n   W i n d o w s 
 
 U s e r T e m p = F i l e   T e m p   D I b u a t   o l e h   A p l i k a s i 
 
 C h r o m e C a c h e = 
 
 C h r o m e C o o k i e s = C o o k i e   G o o g l e   C h r o m e 
 
 C h r o m e F o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   G o o g l e   C h r o m e 
 
 C h r o m e l o c a l s t o r a g e = R i w a y a t   G o o g l e   C h r o m e 
 
 C h r o m e P a s s w o r d s = P a s s w o r d   T e r s i m p a n   G o o g l e   C h r o m e 
 
 C h r o m e S e s s i o n = 
 
 F i r e F o x C a c h e = 
 
 F i r e F o x C o o k i e s = C o o k i e   M o z i l l a   F i r e f o x 
 
 F i r e F o x F o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   M o z i l l a   F i r e f o x 
 
 F i r e F o x O t h e r = R i w a y a t   L a i n   M o z i l l a   F i r e f o x 
 
 F i r e F o x P a s s w o r d s = P a s s w o r d   T e r s i m p a n   M o z i l l a   F i r e f o x 
 
 L a s t D o w n l o a d D i r = L o k a s i   U n d u h a n   T e r a k h i r 
 
 I E A d d r e s s B a r H i s t o r y = R i w a y a t   b i l a h   a l a m a t   M i c r o s o f t   I n t e r n e t   E x p l o r e r 
 
 I E C a h c e = 
 
 I E C o o k i e s = C o o k i e   M i c r o s o f t   I E 
 
 I E F o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   I E 
 
 I E H i s t o r y = R i w a y a t   A k t i f   M i c r o s o f t   I n t e r n e t   E x p l o r e r 
 
 I E P a s s w o r d s = P a s s w o r d   T e r s i m p a n   I E 
 
 O p e r a C a c h e = 
 
 O p e r a C o o k i e s = C o o k i e   O p e r a 
 
 O p e r a H i s t o r y = R i w a y a t   O p e r a 
 
 O p e r a P a s s w o r d s = P a s s w o r d   T e r s i m p a n   O p e r a 
 
 O p e r a S e s s i o n s = 
 
 O p e r a S i t e I c o n = I k o n   W e b s i t e   O p e r a 
 
 O p e r a U R L H i s t o r y = U R L   t e r k e t i k   O p e r a 
 
 P a l e M o o n C a c h e = 
 
 P a l e M o o n C o o k i e s = C o o k i e   P a l e M o o n 
 
 P a l e M o o n D o w n l o a d H i s t o r y = R i w a y a t   U n d u h a n   P a l e M o o n 
 
 P a l e M o o n H i s t o r y = R i w a y a t   P a l e M o o n 
 
 P a l e M o o n S a v e d F o r m I n f o r m a t i o n = I n f o r m a s i   T e r s i m p a n   F o r m   P a l e M o o n 
 
 P a l e M o o n S e s s i o n = 
 
 P a l e M o o n S i t e P r e f e r e n c e s = P r e f e r e n s i   S i t u s   P a l e M o o n 
 
 V i v a l d i = 
 
 V i v a l d i C a c h e = 
 
 V i v a l d i C o o k i e s = C o o k i e   V i v a l d i 
 
 V i v a l d i F o r m = R i w a y a t   F o r m   P e n g i s i s a n   O t o m a t i s   V i v a l d i 
 
 V i v a l d i l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   V i v a l d i 
 
 V i v a l d i S a v e d P a s s w o r d s = P a s s w o r d   T e r s m i p a n   V i v a l d i 
 
 V i v a l d i S e s s i o n = 
 
 Y a n d e x = 
 
 Y a n d e x C a c h e = 
 
 Y a n d e x C o o k i e s = C o o k i e   Y a n d e x 
 
 Y a n d e x F o r m = R i w a y a t   F o r m   P e n g i s i s a n   O t o m a t i s     Y a n d e x 
 
 Y a n d e x L o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   Y a n d e x 
 
 Y a n d e x P a s s w o r d s = P a s s w o r d   T e r s i m p a n   Y a n d e x 
 
 Y a n d e x S e s s i o n = 
 
 B r a v e B r o w s e r = 
 
 B r a v e B r o w s e r C a c h e = 
 
 B r a v e C o o k i e s = C o o k i e   B r a v e   B r o w s e r 
 
 B r a v e f o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s     B r a v e   B r o w s e r 
 
 B r a v e B r o w s e r l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   B r a v e   B r o w s e r 
 
 B r a v e P a s s w o r d s = P a s s w o r d   T e r s i m p a n 
 
 B r a v e S e s s i o n = 
 
 M S E d g e L e g a c y C a c h e = 
 
 M S E d g e L e g a c y H i s t o r y = R i w a y a t   M S   E d g e   L e g a c y 
 
 M S E d g e L e g a c y D o w n l o a d H i s t o r y = R i w a y a t   U n d u h a n   M S   E d g e   L e g a c y 
 
 M S E d g e L e g a c y T y p e d U R L s = U R L   T e r k e t i k   M S   E d g e   L e g a c y 
 
 M S E d g e L e g a c y S a v e d F o r m I n f o r m a t i o n = F o r m   I n f o r m a s i   T e r s i m p a n   M S   E d g e   L e g a c y 
 
 M S E d g e L e g a c y S a v e d P a s s w o r d s = P a s s w o r d   T e r s i m p a n   M S   E d g e   L e g a c y 
 
 M S E d g e L e g a c y S e s s i o n = 
 
 M S E d g e L e g a c y C o o k i e s = C o o k i e   M S   E d g e   L e g a c y 
 
 M S E d g e C h r o m i u m C a c h e = 
 
 M S E d g e C h r o m i u m l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   M S   E d g e   C h r o m i u m 
 
 M S E d g e C h r o m i u m f o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s 
 
 M S E d g e C h r o m i u m P a s s w o r d s = P a s s w o r d   T e r s i m p a n   M S   E d g e   C h r o m i u m 
 
 M S E d g e C h r o m i u m s e s s i o n = 
 
 M S E d g e C h r o m i u m C o o k i e s = C o o k i e   M S   E d g e   C h r o m i u m 
 
 M S E d g e D e v C a c h e = 
 
 M S E d g e D e v l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   M S   E d g e   D e v 
 
 M S E d g e D e v f o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   M S   E d g e   D e v 
 
 M S E d g e D e v P a s s w o r d s = P a s s w o r d   T e r s i m p a n   M S   E d g e   D e v 
 
 M S E d g e D e v s e s s i o n = 
 
 M S E d g e D e v C o o k i e s = C o o k i e   M S   E d g e   D e v 
 
 M S E d g e B e t a C a c h e = 
 
 M S E d g e B e t a l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   M S   E d g e   B e t a 
 
 M S E d g e B e t a f o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   M S   E d g e   B e t a 
 
 M S E d g e B e t a P a s s w o r d s = P a s s w o r d   T e r s i m p a n   M S   E d g e   B e t a 
 
 M S E d g e B e t a s e s s i o n = 
 
 M S E d g e B e t a C o o k i e s = C o o k i e   M S   E d g e   B e t a 
 
 M S E d g e C a n a r y C a c h e = 
 
 M S E d g e C a n a r y l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   M S   E d g e 
 
 M S E d g e C a n a r y f o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   M S   E d g e   C a n a r y 
 
 M S E d g e C a n a r y P a s s w o r d s = P a s s w o r d   T e r s i m p a n   M S   E d g e   C a n a r y 
 
 M S E d g e C a n a r y s e s s i o n = 
 
 M S E d g e C a n a r y C o o k i e s = C o o k i e   M S   E d g e   C a n a r y 
 
 C o m o d o D r a g o n C a c h = 
 
 C o m o d o D r a g o n l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   C o m o d o   D r a g o n 
 
 C o m o d o D r a g o n F o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   C o m o d o   D r a g o n 
 
 C o m o d o D r a g o n P a s s w o r d s = P a s s w o r d   T e r s i m p a n   C o m o d o   D r a g o n 
 
 C o m o d o D r a g o n S e s s i o n = 
 
 C o m o d o D r a g o n C o o k i e s = C o o k i e   C o m o d o   D r a g o n 
 
 R o c k M e l t C a c h e = 
 
 R o c k M e l t l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   R o c k M e l t 
 
 R o c k M e l t F o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   R o c k M e l t 
 
 R o c k M e l t P a s s w o r d s = P a s s w o r d   T e r s i m p a n   R o c k M e l t 
 
 R o c k M e l t S e s s i o n = 
 
 R o c k M e l t C o o k i e s = C o o k i e   R o c k M e l t 
 
 C e n t B r o w s e r C a c h e = 
 
 C e n t B r o w s e r l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   C e n t B r o w s e r 
 
 C e n t B r o w s e r F o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   C e n t B r o w s e r 
 
 C e n t B r o w s e r P a s s w o r d s = P a s s w o r d   T e r s i m p a n   C e n t B r o w s e r 
 
 C e n t B r o w s e r S e s s i o n = 
 
 C e n t B r o w s e r C o o k i e s = C o o k i e   C e n t B r o w s e r 
 
 C h r o m i u m C a c h e = 
 
 C h r o m i u m l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   C h r o m i u m 
 
 C h r o m i u m F o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   C h r o m i u m 
 
 C h r o m i u m P a s s w o r d s = P a s s w o r d   T e r s i m p a n   C h r o m i u m 
 
 C h r o m i u m S e s s i o n = 
 
 C h r o m i u m C o o k i e s = C o k i e   C h r o m i u m 
 
 T o r c h C a c h e = 
 
 T o r c h l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   T o r c h 
 
 T o r c h F o r m = R i w a y a t   F o r m   P e n g i s i a n   O t o m a t i s   T o r c h 
 
 T o r c h P a s s w o r d s = P a s s w o r d   T e r s i m p a n   T o r c h 
 
 T o r c h S e s s i o n = 
 
 T o r c h C o o k i e s = C o o k i e   T o r c h 
 
 O p e r a G X C a c h e = 
 
 O p e r a G X l o c a l s t o r a g e = P e n y i m p a n a n   L o k a l   O p e r a   G X 
 
 O p e r a G X F o r m = R i w a y a t   F o r m   O p e r a   G X 
 
 O p e r a G X P a s s w o r d s = P a s s w o r d   T e r s i m p a n   O p e r a   G X 
 
 O p e r a G X S e s s i o n = 
 
 O p e r a G X C o o k i e = C o o k i e   O p e r a   G X 
 
 A d o b e A c r o b a t R e a d e r 5 . 0 = 
 
 A d o b e A c r o b a t R e a d e r 6 . 0 = 
 
 A d o b e A c r o b a t R e a d e r 7 . 0 = 
 
 A d o b e R e a d e r 8 . 0 = 
 
 A d o b e R e a d e r 9 . 0 = 
 
 A d o b e R e a d e r 1 1 . 0 = 
 
 A d o b e R e a d e r   D C = 
 
 A d o b e A c r o b a t 8 . 0 = 
 
 A d o b e A c r o b a t 9 . 0 = 
 
 A d o b e A c r o b a t 1 0 . 0 = 
 
 A d o b e A c r o b a t X I = 
 
 A d o b e I m a g e R e a d y 7 . 0 = 
 
 A d o b e I m a g e R e a d y C S = 
 
 A d o b e P h o t o s h o p 6 . 0 = 
 
 A d o b e P h o t o s h o p 7 . 0 = 
 
 A d o b e P h o t o s h o p C S = 
 
 A d o b e P h o t o s h o p C S 2 = 
 
 A d o b e P h o t o s h o p C S 3 = 
 
 A d o b e P h o t o s h o p C S 4 = 
 
 A d o b e P h o t o s h o p C S 5 = 
 
 A d o b e P h o t o s h o p C S 6 = 
 
 A d o b e P h o t o s h o p E l e m e n t s 1 2 = 
 
 A d o b e I l l u s t r a t o r = 
 
 A d o b e A i r = 
 
 A d o b e D r e a m w e a v e r = 
 
 A d o b e P r e m i e r e P r o C C = 
 
 A d o b e P r e m i e r e E l e m e n t s 1 2 = 
 
 A c r o b a t D i s t i l l e r 1 0 = 
 
 A d v a n c e d S e a r c h b a r = 
 
 Y a h o o T o o l b a r = 
 
 W i n d o w s L i v e T o o l b a r = 
 
 G o o g l e T o o l b a r = 
 
 G o o g l e T o o l b a r I E = 
 
 G o o g l e D e s k b a r = 
 
 G o o g l e C a l e n d a r S y n c = 
 
 G o o g l e T a l k = 
 
 K a n t a r i s M e d i a P l a y e r = 
 
 K M P l a y e r = 
 
 W i n d o w s M e d i a P l a y e r = 
 
 W i n d o w s M e d i a C e n t e r = 
 
 R e a l P l a y e r = 
 
 R e a l P l a y e r S P = 
 
 R e a l P l a y e r 1 5 = 
 
 R e a l P l a y e r 1 6 = 
 
 R e a l P l a y e r C l o u d = 
 
 R e a l T i m e s = 
 
 Q u i c k t i m e P l a y e r = 
 
 A V I P r e v i e w = 
 
 S t e a m = 
 
 X f i r e = 
 
 X M L S p y = 
 
 O x y g e n X M L E d i t o r = 
 
 S W i S H = 
 
 P a i n t S h o p P r o 7 . 0 = 
 
 P a i n t S h o p P r o 8 . 0 = 
 
 P a i n t S h o p P r o 9 . 0 = 
 
 P a i n t S h o p P r o X = 
 
 P a i n t S h o p P r o X I = 
 
 P a i n t S h o p P r o X 2 = 
 
 P a i n t S h o p P r o X 3 = 
 
 C o r e l P a i n t S h o p P r o X 4 = 
 
 C o r e l P a i n t S h o p P r o X 5 = 
 
 C o r e l P a i n t S h o p P r o X 6 = 
 
 M S W o r k s 4 . 0 = 
 
 M S O f f i c e 9 7 = 
 
 M S O f f i c e X P = 
 
 M S O f f i c e 2 0 0 3 = 
 
 M S O f f i c e 2 0 0 7 = 
 
 M S O f f i c e 2 0 1 0 = 
 
 M S O f f i c e 2 0 1 3 = 
 
 M S O f f i c e 2 0 1 6 = 
 
 I n s t a l l s h i e l d D e v e l o p e r 7 . 0 = 
 
 M a c r o m e d i a F l a s h 4 . 0 = 
 
 M a c r o m e d i a F l a s h 5 . 0 = 
 
 M a c r o m e d i a F l a s h M X = 
 
 M a c r o m e d i a F l a s h M X 2 0 0 4 = 
 
 A d o b e F l a s h P l a y e r = 
 
 M a c r o m e d i a H o m e s i t e 5 . 0 = 
 
 M a c r o m e d i a F i r e w o r k s 6 . 0 = 
 
 A d o b e F i r e w o r k s 6 . 0 = 
 
 M a c r o m e d i a D r e a m w e a v e r M X = 
 
 M a c r o m e d i a S h o c k w a v e 1 0 = 
 
 M a c r o m e d i a S h o c k w a v e 1 1 = 
 
 M i c r o s o f t S i l v e r l i g h t = 
 
 U l e a d S m a r t S a v e r P r o 3 . 0 = 
 
 N o r t o n A n t i V i r u s = 
 
 S y m a n t e c A n t i V i r u s = 
 
 M S S n a p s h o t V i e w e r = 
 
 M S M a n a g e m e n t C o n s o l e = 
 
 M S W o r d p a d = 
 
 M S P a i n t = 
 
 M S P h o t o E d i t o r = 
 
 M S S e a r c h = 
 
 S e a r c h C h a r m H i s t o r y = 
 
 N e r o B u r n i n g R O M = 
 
 N e r o B u r n i n g R O M   9 = 
 
 N e r o B u r n i n g R O M 1 0 = 
 
 N e r o B u r n i n g R O M 2 0 1 4 = 
 
 N e r o 1 2 P l a t i n u m H D S u i t e = 
 
 N e r o 1 4 P l a t i n u m H D S u i t e = 
 
 N e r o V i d e o 1 1 = 
 
 N e r o V i s i o n 1 0 = 
 
 N e r o B a c k I t U p 2 0 1 4 = 
 
 N e r o E x p r e s s 2 0 1 4 = 
 
 W i n A c e 2 . 0 = 
 
 S p y B o t S e a r c h a n d D e s t r o y = 
 
 A d - A w a r e S E P e r s o n a l = 
 
 A d - A w a r e S E P r o f e s s i o n a l = 
 
 A d - A w a r e S E P l u s = 
 
 A d - A w a r e = 
 
 W e b r o o t S p y S w e e p e r = 
 
 D r i v e r C l e a n e r P r o = 
 
 K a z a a = 
 
 N e t s c a p e N a v i g a t o r 4 . x = 
 
 M i c r o s o f t V i s u a l S t u d i o 6 . 0 = 
 
 A x i a l i s I c o n W o r k s h o p = 
 
 e M u l e S e a r c h H i s t o r y = 
 
 e M u l e F i l e H a s h e s = 
 
 W i n I S O = 
 
 I s o B u s t e r = 
 
 M e d i a P l a y e r C l a s s i c = 
 
 B S P l a y e r = 
 
 V L C M e d i a P l a y e r = 
 
 M e d i a M o n k e y = 
 
 W i n a m p = 
 
 M u s i c M a t c h J u k e b o x = 
 
 S o u n d F o r g e 6 . 0 = 
 
 A u d a c i t y = 
 
 W i n d o w s L i v e M e s s e n g e r = 
 
 S k y p e = 
 
 A O L I n s t a n t M e s s e n g e r = 
 
 C a m f r o g V i d e o C h a t = 
 
 M i r a n d a I n s t a n t M e s s e n g e r = 
 
 P i d g i n = 
 
 Y a h o o M e s s e n g e r = 
 
 o o V o o = 
 
 T e a m S p e a k = 
 
 V e n t r i l o C l i e n t = 
 
 V e n t r i l o S e r v e r = 
 
 M y S p a c e I M = 
 
 A c r o n i s T r u e I m a g e = 
 
 W i n Z i p = 
 
 W i n R A R = 
 
 7 - Z i p = 
 
 B i t Z i p p e r = 
 
 P o w e r A r c h i v e r = 
 
 P o w e r A r c h i v e r 2 0 1 3 = 
 
 Z i p M a g i c = 
 
 P i c o Z i p = 
 
 P K Z i p = 
 
 J a v a = 
 
 F r e s h D o w n l o a d = 
 
 W i n d o w s M o v i e M a k e r = 
 
 T e x t P a d = 
 
 V i r t u a l D u b = 
 
 R e g E d i t = 
 
 G a m e E x p l o r e r = 
 
 A c e H T M L 5 = 
 
 A l c o h o l 1 2 0 = 
 
 A l c o h o l 5 2 = 
 
 L e e c h G e t = 
 
 G e t R i g h t = 
 
 D o w n l o a d A c c e l e r a t o r P l u s = 
 
 F r e e D o w n l o a d M a n a g e r = 
 
 I n t e r n e t D o w n l o a d A c c e l e r a t o r = 
 
 I n t e r n e t D o w n l o a d M a n a g e r = 
 
 O r b i t D o w n l o a d e r = 
 
 M o r p h e u s = 
 
 V N C V i e w e r 3 = 
 
 V N C V i e w e r 4 = 
 
 D V D S h r i n k = 
 
 T i v o D e s k t o p = 
 
 C A A n t i - V i r u s = 
 
 Z o n e A l a r m = 
 
 G o o g l e E a r t h = 
 
 P e r f e c t D i s k 7 . 0 = 
 
 D a e m o n T o o l s = 
 
 V u z e = 
 
 B i t T o r r e n t = 
 
 F r o s t W i r e = 
 
 u T o r r e n t = 
 
 S h a r e a z a = 
 
 i M e s h = 
 
 B e a r S h a r e = 
 
 D C + + = 
 
 A r e s = 
 
 C u t e F T P P r o 7 . 0 = 
 
 C u t e F T P H o m e 7 . 0 = 
 
 C u t e F T P P r o 8 . 0 = 
 
 C u t e F T P H o m e 8 . 0 = 
 
 C u t e F T P 9 = 
 
 C o r e F T P = 
 
 F i l e Z i l l a = 
 
 S m a r t F T P = 
 
 C l a m W i n = 
 
 E w i d o A n t i - M a l w a r e = 
 
 A V G A n t i - S p y w a r e = 
 
 M a l w a r e b y t e s A n t i - M a l w a r e = 
 
 S p y w a r e T e r m i n a t o r = 
 
 S U P E R A n t i S p y w a r e = 
 
 A - S q u a r e d = 
 
 F o x i t R e a d e r = 
 
 F o x i t R e a d e r 6 . 0 = 
 
 F o x i t R e a d e r 7 . 0 = 
 
 F o x i t R e a d e r 8 . 0 = 
 
 F o x i t R e a d e r 9 . 0 = 
 
 P a i n t . N E T = 
 
 O p e n O f f i c e 1 . 1 4 = 
 
 O p e n O f f i c e 2 . 0 = 
 
 O p e n O f f i c e 2 . 1 = 
 
 O p e n O f f i c e 3 = 
 
 O p e n O f f i c e 4 = 
 
 G r i s o f t A V G 7 . 0 = 
 
 A V G A n t i V i r u s 8 . 0 = 
 
 A V G A n t i V i r u s 9 . 0 = 
 
 A V G A n t i V i r u s 1 0 . 0 = 
 
 A V G A n t i V i r u s 2 0 1 2 = 
 
 A V G A n t i V i r u s 2 0 1 3 = 
 
 A n t i V i r D e s k t o p = 
 
 A v a s t A n t i v i r u s 4 = 
 
 A v a s t A n t i v i r u s 5 = 
 
 A v a s t A n t i v i r u s = 
 
 B i t D e f e n d e r = 
 
 T U G Z i p = 
 
 W i n d o w s D e f e n d e r = 
 
 I Z A r c = 
 
 M S S e a r c h H e l p e r E x t e n s i o n = 
 
 M S O f f i c e P i c t u r e M a n a g e r = 
 
 I m g B u r n = 
 
 C l o n e C D = 
 
 V i r t u a l C l o n e D r i v e = 
 
 W i n P a t r o l = 
 
 L o g M e I n = 
 
 A s h a m p o o B u r n i n g S t u d i o 1 0 = 
 
 A s h a m p o o B u r n i n g S t u d i o 1 1 = 
 
 A s h a m p o o B u r n i n g S t u d i o 1 4 = 
 
 E x a m D i f f = 
 
 E x a m D i f f P r o = 
 
 C o m p a r e I t = 
 
 W i n D i f f = 
 
 F e e d D e m o n = 
 
 L a s t . F M = 
 
 P a n d o = 
 
 S a n d b o x i e = 
 
 S n a g i t 9 = 
 
 S n a g i t 1 0 = 
 
 S n a g i t 1 1 = 
 
 S n a g i t 1 2 = 
 
 D i t t o = 
 
 E v e r n o t e = 
 
 I 2 P = 
 
 M c A f e e A n t i V i r u s = 
 
 P e r f e c t D i s k 8 = 
 
 P e r f e c t D i s k 9 = 
 
 P e r f e c t D i s k 1 0 = 
 
 P e r f e c t D i s k 1 1 = 
 
 P e r f e c t D i s k 1 2 = 
 
 P e r f e c t D i s k 1 2 . 5 = 
 
 P o w e r I S O = 
 
 U l t r a I S O = 
 
 G I M P 2 . 4 = 
 
 G I M P 2 . 6 = 
 
 G I M P 2 . 8 = 
 
 G o Z i l l a = 
 
 M a g i c I S O = 
 
 Z u n e = 
 
 B r e e z e B r o w s e r P r o = 
 
 F a s t S t o n e I m a g e V i e w e r = 
 
 F a s t S t o n e C a p t u r e = 
 
 A k e l P a d = 
 
 N o t e p a d + + = 
 
 N o t e X p a d = 
 
 R e g E d i t X = 
 
 F o x i t R e a d e r 5 . 0 = 
 
 A C D S e e 1 4 = 
 
 C a m t a s i a S t u d i o 7 . 0 = 
 
 C a m t a s i a S t u d i o 8 = 
 
 C o f f e e C u p H T M L E d i t o r = 
 
 A n y V i d e o C o n v e r t e r U l t i m a t e = 
 
 F r e e m a k e V i d e o D o w n l o a d e r = 
 
 F r e e m a k e A u d i o C o n v e r t e r = 
 
 F r e e m a k e V i d e o C o n v e r t e r = 
 
 C o n v e r t X T o D V D = 
 
 V S O B l u - r a y C o n v e r t e r U l t i m a t e = 
 
 V S O D V D C o n v e r t e r U l t i m a t e = 
 
 A I M P 3 = 
 
 C o r e l V i d e o S t u d i o P r o X 4 = 
 
 C o r e l V i d e o S t u d i o P r o X 5 = 
 
 A l Z i p = 
 
 C y b e r L i n k P h o t o D i r e c t o r 3 = 
 
 C y b e r L i n k P o w e r D i r e c t o r 1 0 = 
 
 C y b e r L i n k P o w e r D i r e c t o r 1 2 = 
 
 C y b e r L i n k A u d i o D i r e c t o r 4 = 
 
 D i v X p l a y e r = 
 
 U n i v e r s a l E x t r a c t o r = 
 
 4 S y n c = 
 
 C o p e r n i c D e s k t o p S e a r c h = 
 
 D V D F a b = 
 
 I n k S c a p e = 
 
 A n o n y m i z e r = 
 
 G a d u - G a d u = 
 
 F o x i t P h a n t o m P D F = 
 
 P D F C r e a t o r = 
 
 P D F A r c h i t e c t = 
 
 M a i l W a s h e r P r o = 
 
 S a m s u n g K i e s = 
 
 C o n n e c t i f y H o t s p o t = 
 
 E x p r e s s S c r i b e = 
 
 G o m P l a y e r = 
 
 S o n y V e g a s P r o 1 2 . 0 = 
 
 D i r e c t o r y O p u s 1 0 = 
 
 N V I D I A I n s t a l l F i l e s = 
 
 C D B u r n e r X P = 
 
 U l t r a E d i t = 
 
 P h o t o d e x P r o S h o w P r o d u c e r = 
 
 S k e t c h U p M a k e 2 0 1 3 = 
 
 H a n d B r a k e = 
 
 A B B Y Y F i n e R e a d e r 1 1 . 0 = 
 
 T h e B a t = 
 
 W o n d e r s h a r e V i d e o C o n v e r t e r U l t i m a t e = 
 
 S u b l i m e T e x t = 
 
 P i c a s a 3 = 
 
 K i n g s o f t O f f i c e 2 0 1 3 = 
 
 X i l i s o f t V i d e o C o n v e r t e r = 
 
 X i l i s o f t D V D R i p p e r = 
 
 d B p o w e r a m p = 
 
 S y n c B a c k F r e e = 
 
 W o n d e r s h a r e V i d e o C o n v e r t e r P r o = 
 
 A i m e r s o f t V i d e o C o n v e r t e r U l t i m a t e = 
 
 S t a r d o c k W i n d o w B l i n d s = 
 
 N i t r o P r o 9 = 
 
 N i t r o P D F R e a d e r 3 = 
 
 V i r t u a l D J 7 = 
 
 F o r m a t F a c t o r y 3 . 3 . 5 = 
 
 T e a m V i e w e r = 
 
 M S O n e D r i v e = 
 
 V N C V i e w e r 5 = 
 
 T a g & R e n a m e 3 . 8 = 
 
 T a n g o = 
 
 P h o t o S c a p e 3 . 6 . 5 = 
 
 B l u e S t a c k s = 
 
 M a n y C a m 4 . 0 . 1 0 9 = 
 
 X n V i e w = 
 
 A p p l e I n s t a l l C a c h e = 
 
 B a i d u Y u n G u a n j i a = 
 
 B a t t l e . n e t = 
 
 D r i v e T h e L i f e = 
 
 i T u n e s = 
 
 k p z s - c a c h e = 
 
 Q I H U 3 6 0 T o t a l S e c u r i t y = 
 
 T e n c e n t - L o g s = 
 
 T h u n d e r = 
 
 T h u n d e r - G a m e = 
 
 T h u n d e r X M P = 
 
 Y o d a o - D i c t = 
 
 Y Y = 
 
 D r i v e r G e n i u s U p d a t e F i l e s = 
 
 D r i v e r G e n i u s B a c k u p F i l e s = 
 
 D r i v e r G e n i u s D o w n l o a d I n s t a l l e r F i l e s = 
 
 D r i v e r G e n i u s C a c h e F i l e s = 
 
 Q Q M u s i c C a c h e F i l e s = 
 
 K u G o u M u s i c C a c h e F i l e s = 
 
 K u G o u L y r i c F i l e s = 
 
 X I A M I M u s i c C a c h e = 
 
 N e t e a s e C l o u d M u s i c C a c h e = 
 
 K u W o I n s t a l l C a c h e = 
 
 K u W o M u s i c C a c h e = 
 
 B a i d u M u s i c L o g F i l e s = 
 
 B a i d u M u s i c C a c h e = 
 
 W e S i n g L o g F i l e s = 
 
 W e S i n g C a c h e = 
 
 Q Q L i v e C a c h e F i l e s = 
 
 Y o u k u C a c h e F i l e s = 
 
 I Q I Y I V i d e o C a c h e F i l e s = 
 
 m g t v L o g F i l e s = 
 
 m g t v C a c h e F i l e s = 
 
 C N T V U p d a t e L o g F i l e s = 
 
 S O H U P l a y e r C a c h e a n d L o g F i l e s = 
 
 S O H U P l a y e r D o w n l o a d s C a c h e = 
 
 S t o r m P l a y e r T e m p F i l e s = 
 
 P P T V T e m p F i l e s = 
 
 B a i d u P l a y e r = 
 
 F u n s h i o n P l a y e r = 
 
 Q Q C a c h e F i l e s = 
 
 Q Q r e c e i v e d i m a g e s = 
 
 Q Q r e c e i v e d f i l e s = 
 
 Q Q r e c e i v e d D y n a m i c E m o t i c o n = 
 
 Q Q r e c e i v e d V i d e o = 
 
 Q Q r e c e i v e d A u d i o = 
 
 T I M C a c h e F i l e s = 
 
 Q T a l k C a c h e F i l e s = 
 
 Q Q P C M g r = 
 
 T e n c e n t Q Q G a m e = 
 
 [ T y p e ] 
 
 U s e r D e f i n e = 
 
 Z e r o F i l e = F i l e   k o s o n g   -   0   b y t e s 
 
 W i n S e t u p L o g = F i l e   l o g   k e s a l a h a n   i n s t a l a s i   w i n d o w s 
 
 N t U n i n s t a l l = P e n c o p o t a n   s u m b e r   P a t h   W i n d o w s 
 
 I n v a l i d L n k = P i n t a s a n   t i d a k   v a l i d 
 
 U s e r T e m p = F i l e   d a l a m   f o l d e r   s e m e n t a r a   P e n g g u n a 
 
 W i n T e m p = F i l e   d a l a m   f o l d e r   s e m e n t a r a   W i n d w o w s 
 
 R e c e n t F i l e s = T a u t a n   f i l e   t e r b a r u 
 
 P a t c h S o u r c e = S u m b e r   W i n d o w s   U p d a t e 
 
 T e m p F i l e = F i l e   s e m e n t a r a 
 
 f c w d i r = F i l e   y a n g   d i b u a t   o l e h   p r o s e s   p e n g u n d u h a n 
 
 t f c b p = F i l e   s e m e n t a r a   y a n g   d i b u a t   o l e h   p r o g r a m 
 
 s d r f = F i l e   p e m u l i h a n   y a n g   d i b u a t   s e l a m a   p e m i n d a i a n   d i s k 
 
 O l d F i l e = F i l e   l a m a 
 
 o u t d a t e d f i l e = F i l e   k a d a l u a r s a 
 
 f c w h f i r = F i l e   y a n g   d i b u a t   s a a t   f i l e   b a n t u a n   b e r j a l a n 
 
 N c f i l e = F i l e   c a c h e   n e w s g r o u p 
 
 B a c k u p f i l e = F i l e   c a d a n g a n 
 
 W B f i l e = F i l e   c a d a n g a n   M S   W o r d 
 
 C b s w a h f = F i l e   y a n g   d i b u a t   s a a t   m e n c a r i   f i l e   b a n t u a n 
 
 w h t f = F i l e   s e m e n t a r a   s i s t e m   b a n t u a n   W i n d o w s 
 
 L o g f i l e = F i l e   l o g 
 
 L L o g f i l e = F i l e   l o g   y a n g   d i b u a t   o l e h   a p l i k a s i   a t a u   s e r v i s   s i s t e m . 
 
 E r r o r f i l e = F i l e   e r r o r 
 
 W m d f = F i l e   s a m p a h   m e m o r i   W i n 2 0 0 0 / X P 
 
 b c o m f = F i l e   m o d i f i k a s i   a t a u   s a l i n a n   c a d a n g a n 
 
 I n d e x f i l e = F i l e   i n d e k s 
 
 i f c b s i o t i t p b s = F i l e s   i n d e k s   y a n g   d i b u a t   o l e h   s i s t e m   u n t u k   m e n i n g k a t k a n   k e c e p a t a n   p e n e l u s u r a n   g a m b a r 
 
 T f c d s = F i l e   s e m e n t a r a   y a n g   d i b u a t   s e l a m a   s e t u p 
 
 I n t e r m e d i a t e f i l e = F i l e   p e r a n t a r a 
 
 c h k f i l e = F i l e   y a n g   d i p u l i h k a n 
 
 [ A u t o C l e a n ] 
 
 D i s k S a v e d = R u a n g   p e n y i m p a n a n   t e l a h   d i s i m p a n . 
 
 T i t l e = S e l a m a t ! 
 
 T r a c e C l e a n n e d = R i w a y a t   t e l a h   d i b e r s i h k a n 
 
 [ S k i n ] 
 
 S k i n D e f a u l t = B a k u 
 
 S k i n G r a y = A b u - a b u 
 
 S k i n O r a n g e = J i n g g a 
 
 S k i n C u s t o m = K u s t o m 
 
 S k i n C o l o r = K u s t o m   w a r n a   k u l i t 
 
 [ A u t o U p d a t e ] 
 
 C u r r e n t F i l e = F i l e   s e k a r a n g 
 
 C a n c e l i n g = M e m b a t a l k a n . . . 
 
 C A N C E L = B a t a l 
 
 R e a d y = S i a p   u n t u k   d i u n d u h 
 
 [ E U A S K ] 
 
 r e g c a p t i o n = D a f t a r 
 
 l o g i n c a p t i o n = M a s u k 
 
 F r m E u a s k L o g i n . l b l J o i n . C a p t i o n = G a b u n g   E u a s k 
 
 F r m E u a s k L o g i n . l b l S i g n . C a p t i o n = M a u s k   k e   E u a s k 
 
 F r m E u a s k L o g i n . l b l n i c k . C a p t i o n = U s e r n a m e : 
 
 F r m E u a s k L o g i n . l b l 1 . C a p t i o n = P a s s w o r d : 
 
 F r m E u a s k L o g i n . b t n l o g i n . c a p t i o n = M a s u k 
 
 F r m E u a s k L o g i n . b t n r e g i s t e r . C a p t i o n = D a f t a r 
 
 F r m E u a s k L o g i n . b t n p w d . C a p t i o n = L u p a   p a s s w o r d ? 
 
 F r m E u a s k L o g i n . W i s e L a b e l 1 . C a p t i o n = U s e r n a m e : 
 
 F r m E u a s k L o g i n . W i s e L a b e l 2 . C a p t i o n = P a s s w o r d : 
 
 F r m E u a s k L o g i n . b t n r e g . c a p t i o n = D a f t a r 
 
 F r m E u a s k L o g i n . W i s e S i m p l e B u t t o n 1 . C a p t i o n = M a s u k 
 
 F r m E u a s k L o g i n . l b l 2 . C a p t i o n = E m a i l : 
 
 F r m E u a s k L o g i n . b t n n o n e . C a p t i o n = N a n t i 
 
 i n p u t u s e r = S i l a k a n   m a s u k k a n   u s e r n a m e   d a n   p a s s w o r d   a n d a . 
 
 