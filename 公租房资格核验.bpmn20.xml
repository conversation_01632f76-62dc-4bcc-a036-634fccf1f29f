<?xml version='1.0' encoding='UTF-8'?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:flowable="http://flowable.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:omgdc="http://www.omg.org/spec/DD/20100524/DC" xmlns:omgdi="http://www.omg.org/spec/DD/20100524/DI" typeLanguage="http://www.w3.org/2001/XMLSchema" expressionLanguage="http://www.w3.org/1999/XPath" targetNamespace="default">
  <process id="rent_apply_check" name="公租房资格核验" isExecutable="true">
    <startEvent id="startEvent1" flowable:initiator="initiator"/>
    <userTask id="sc9a2e6650" name="发起核查申请" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="s431ee7409" sourceRef="startEvent1" targetRef="sc9a2e6650"/>
    <parallelGateway id="s8225d22b1"/>
    <sequenceFlow id="s94f30282c" sourceRef="sc9a2e6650" targetRef="s8225d22b1"/>
    <userTask id="s46bec11b2" name="房管机构核查" flowable:candidateGroups="FG_JG">
      <extensionElements>
        <modeler:group-info-name-FG_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（房管机构） (FG_JG)]]></modeler:group-info-name-FG_JG>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s9dcf18767" name="省直单位核查" flowable:candidateUsers="${hsQwApplyService.findUserByRoleAndOffice(officeCode,'DZ_JG')}"/>
    <parallelGateway id="s9d731080c"/>
    <sequenceFlow id="s564ac9350" sourceRef="s46bec11b2" targetRef="s9d731080c"/>
    <userTask id="sf2a38015a" name="经办核查" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s231cbc5bc" name="处室领导复核" flowable:candidateGroups="HS_CSLD">
      <extensionElements>
        <modeler:group-info-name-HS_CSLD xmlns:modeler="http://flowable.org/modeler"><![CDATA[处室领导（住房保障处） (HS_CSLD)]]></modeler:group-info-name-HS_CSLD>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="sd68b778cb"/>
    <userTask id="sbc2fd509b" name="合同续签确认" flowable:assignee="${applyer}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <userTask id="s6893f2c72" name="核查结果确认" flowable:assignee="${applyer}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="s3c4adb6b8"/>
    <userTask id="sbea96d827" name="合同续签" flowable:candidateUsers="${hsQwApplyService.findUserByRoleAndOffice(officeCode,'DZ_JG')}"/>
    <serviceTask id="s623cee702" name="发起退租" flowable:delegateExpression="${hsQwApplyCheckCompactServiceTask}"/>
    <endEvent id="sbb62a2860"/>
    <exclusiveGateway id="s92b7f6359"/>
    <userTask id="s2253b900c" name="申诉知悉" flowable:candidateGroups="HS_JG,HS_ZFJB_GZF">
      <extensionElements>
        <modeler:group-info-name-HS_JG xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办（住房保障处） (HS_JG)]]></modeler:group-info-name-HS_JG>
        <modeler:group-info-name-HS_ZFJB_GZF xmlns:modeler="http://flowable.org/modeler"><![CDATA[经办-公租房(住房保障处) (HS_ZFJB_GZF)]]></modeler:group-info-name-HS_ZFJB_GZF>
        <modeler:activiti-idm-candidate-group xmlns:modeler="http://flowable.org/modeler"><![CDATA[true]]></modeler:activiti-idm-candidate-group>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <exclusiveGateway id="s8ee62335d"/>
    <sequenceFlow id="s7c3091472" name="是否合理" sourceRef="s2253b900c" targetRef="s8ee62335d"/>
    <sequenceFlow id="s10834449f" sourceRef="s623cee702" targetRef="sbb62a2860"/>
    <sequenceFlow id="sea2937fb2" sourceRef="sbea96d827" targetRef="sbb62a2860"/>
    <sequenceFlow id="s9cc408cdd" name="是否申诉" sourceRef="s6893f2c72" targetRef="s92b7f6359"/>
    <sequenceFlow id="se466797cb" name="是否续签" sourceRef="sbc2fd509b" targetRef="s3c4adb6b8"/>
    <sequenceFlow id="sef7e23315" name="续签" sourceRef="s3c4adb6b8" targetRef="sbea96d827">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${renewal ==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s261430d98" name="申诉" sourceRef="s92b7f6359" targetRef="s2253b900c">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${complaint==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s4f88b6bed" name="不合理" sourceRef="s8ee62335d" targetRef="s623cee702">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${reasonable!=0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s23fc10e84" name="不申诉" sourceRef="s92b7f6359" targetRef="s623cee702">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${complaint!=0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sdf1fe8992" name="合规" sourceRef="sd68b778cb" targetRef="s231cbc5bc">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${violation==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s46c6e5abd" name="不合规" sourceRef="sd68b778cb" targetRef="s6893f2c72">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${violation!=0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="s3636f0c01" name="是否合格" sourceRef="sf2a38015a" targetRef="sd68b778cb"/>
    <sequenceFlow id="s6cedab44c" name="合理" sourceRef="s8ee62335d" targetRef="sbb62a2860">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${reasonable==0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sb3f1c95e2" sourceRef="s8225d22b1" targetRef="s46bec11b2"/>
    <sequenceFlow id="s6a312007d" sourceRef="s9d731080c" targetRef="sf2a38015a"/>
    <userTask id="se4cf71a03" name="个人补充核查证明材料" flowable:assignee="${applyer}">
      <extensionElements>
        <modeler:initiator-can-complete xmlns:modeler="http://flowable.org/modeler"><![CDATA[false]]></modeler:initiator-can-complete>
      </extensionElements>
    </userTask>
    <sequenceFlow id="s5295e1573" sourceRef="s8225d22b1" targetRef="se4cf71a03"/>
    <sequenceFlow id="s6eef9e17c" sourceRef="se4cf71a03" targetRef="s9dcf18767"/>
    <exclusiveGateway id="s282364eab"/>
    <sequenceFlow id="s4e5a1f959" name="是否政策性购房" sourceRef="s9dcf18767" targetRef="s282364eab"/>
    <sequenceFlow id="s925d5f6f3" name="是" sourceRef="s282364eab" targetRef="s6893f2c72">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${policyBuy==1 || compRecv == 1 || pubRent == 1}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sf58daac6d" name="否" sourceRef="s282364eab" targetRef="s9d731080c">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${policyBuy==0 && compRecv == 0 && pubRent == 0}]]></conditionExpression>
    </sequenceFlow>
    <exclusiveGateway id="sda8f6b5ec"/>
    <sequenceFlow id="sc168339e9" name="不续签" sourceRef="s3c4adb6b8" targetRef="s623cee702">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${renewal!=0}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="se36a13a86" name="三个月到期" sourceRef="s231cbc5bc" targetRef="sda8f6b5ec"/>
    <sequenceFlow id="s2071301ca" name="是" sourceRef="sda8f6b5ec" targetRef="sbc2fd509b">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${isDueTime}]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="sbba78a532" name="否" sourceRef="sda8f6b5ec" targetRef="sbb62a2860">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[${!isDueTime}]]></conditionExpression>
    </sequenceFlow>
  </process>
  <bpmndi:BPMNDiagram id="BPMNDiagram_rent_apply_check">
    <bpmndi:BPMNPlane bpmnElement="rent_apply_check" id="BPMNPlane_rent_apply_check">
      <bpmndi:BPMNShape bpmnElement="startEvent1" id="BPMNShape_startEvent1">
        <omgdc:Bounds height="30.0" width="30.0" x="120.0" y="145.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sc9a2e6650" id="BPMNShape_sc9a2e6650">
        <omgdc:Bounds height="80.0" width="100.0" x="285.0" y="120.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s8225d22b1" id="BPMNShape_s8225d22b1">
        <omgdc:Bounds height="40.0" width="40.0" x="315.0" y="270.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s46bec11b2" id="BPMNShape_s46bec11b2">
        <omgdc:Bounds height="80.0" width="100.0" x="285.0" y="405.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s9dcf18767" id="BPMNShape_s9dcf18767">
        <omgdc:Bounds height="80.0" width="100.0" x="825.0" y="250.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s9d731080c" id="BPMNShape_s9d731080c">
        <omgdc:Bounds height="40.0" width="40.0" x="615.0" y="425.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sf2a38015a" id="BPMNShape_sf2a38015a">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="525.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s231cbc5bc" id="BPMNShape_s231cbc5bc">
        <omgdc:Bounds height="80.0" width="100.0" x="285.0" y="685.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sd68b778cb" id="BPMNShape_sd68b778cb">
        <omgdc:Bounds height="40.0" width="40.0" x="615.0" y="705.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbc2fd509b" id="BPMNShape_sbc2fd509b">
        <omgdc:Bounds height="80.0" width="100.0" x="285.0" y="960.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s6893f2c72" id="BPMNShape_s6893f2c72">
        <omgdc:Bounds height="80.0" width="100.0" x="825.0" y="685.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s3c4adb6b8" id="BPMNShape_s3c4adb6b8">
        <omgdc:Bounds height="40.0" width="40.0" x="315.0" y="1102.5000657141247"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbea96d827" id="BPMNShape_sbea96d827">
        <omgdc:Bounds height="80.0" width="100.0" x="285.0" y="1200.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s623cee702" id="BPMNShape_s623cee702">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="1082.5000657141247"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sbb62a2860" id="BPMNShape_sbb62a2860">
        <omgdc:Bounds height="28.0" width="28.0" x="479.25104676062233" y="1396.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s92b7f6359" id="BPMNShape_s92b7f6359">
        <omgdc:Bounds height="40.0" width="40.0" x="855.0" y="833.7500485032825"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s2253b900c" id="BPMNShape_s2253b900c">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="813.7500485032825"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s8ee62335d" id="BPMNShape_s8ee62335d">
        <omgdc:Bounds height="40.0" width="40.0" x="615.0" y="980.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="se4cf71a03" id="BPMNShape_se4cf71a03">
        <omgdc:Bounds height="80.0" width="100.0" x="585.0" y="250.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="s282364eab" id="BPMNShape_s282364eab">
        <omgdc:Bounds height="40.0" width="40.0" x="855.0" y="425.0"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="sda8f6b5ec" id="BPMNShape_sda8f6b5ec">
        <omgdc:Bounds height="40.0" width="40.0" x="315.0" y="833.7500485032825"/>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="se466797cb" id="BPMNEdge_se466797cb">
        <omgdi:waypoint x="335.0" y="1039.95"/>
        <omgdi:waypoint x="335.0" y="1102.5000657141247"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s5295e1573" id="BPMNEdge_s5295e1573">
        <omgdi:waypoint x="354.4785105315708" y="290.46822742474916"/>
        <omgdi:waypoint x="584.9999999999876" y="290.08338898163606"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sea2937fb2" id="BPMNEdge_sea2937fb2">
        <omgdi:waypoint x="335.0" y="1279.95"/>
        <omgdi:waypoint x="335.0" y="1328.0"/>
        <omgdi:waypoint x="493.25104676062233" y="1328.0"/>
        <omgdi:waypoint x="493.25104676062233" y="1396.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s925d5f6f3" id="BPMNEdge_s925d5f6f3">
        <omgdi:waypoint x="875.4659498207885" y="464.48055356503045"/>
        <omgdi:waypoint x="875.0714669051879" y="685.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sef7e23315" id="BPMNEdge_sef7e23315">
        <omgdi:waypoint x="335.0" y="1142.4415835665277"/>
        <omgdi:waypoint x="335.0" y="1200.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="se36a13a86" id="BPMNEdge_se36a13a86">
        <omgdi:waypoint x="335.0" y="764.9499999999999"/>
        <omgdi:waypoint x="335.0" y="833.7500485032825"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sb3f1c95e2" id="BPMNEdge_sb3f1c95e2">
        <omgdi:waypoint x="335.0" y="309.9435686653771"/>
        <omgdi:waypoint x="335.0" y="405.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sdf1fe8992" id="BPMNEdge_sdf1fe8992">
        <omgdi:waypoint x="615.0" y="725.0"/>
        <omgdi:waypoint x="384.94999999979433" y="725.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s431ee7409" id="BPMNEdge_s431ee7409">
        <omgdi:waypoint x="149.9499995430215" y="160.0"/>
        <omgdi:waypoint x="285.0" y="160.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s6a312007d" id="BPMNEdge_s6a312007d">
        <omgdi:waypoint x="635.0" y="464.94169442131556"/>
        <omgdi:waypoint x="635.0" y="525.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s3636f0c01" id="BPMNEdge_s3636f0c01">
        <omgdi:waypoint x="635.0" y="604.9499999999999"/>
        <omgdi:waypoint x="635.0" y="705.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s94f30282c" id="BPMNEdge_s94f30282c">
        <omgdi:waypoint x="335.0" y="199.95"/>
        <omgdi:waypoint x="335.0" y="270.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s7c3091472" id="BPMNEdge_s7c3091472">
        <omgdi:waypoint x="635.0" y="893.7000485032825"/>
        <omgdi:waypoint x="635.0" y="980.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sc168339e9" id="BPMNEdge_sc168339e9">
        <omgdi:waypoint x="354.9466761063947" y="1122.5000657141247"/>
        <omgdi:waypoint x="585.0" y="1122.5000657141247"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s261430d98" id="BPMNEdge_s261430d98">
        <omgdi:waypoint x="855.0" y="853.7500485032825"/>
        <omgdi:waypoint x="684.9499999994363" y="853.7500485032825"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sf58daac6d" id="BPMNEdge_sf58daac6d">
        <omgdi:waypoint x="855.4583333333334" y="445.45833333333337"/>
        <omgdi:waypoint x="654.8629202988675" y="445.0413900414938"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s2071301ca" id="BPMNEdge_s2071301ca">
        <omgdi:waypoint x="335.43459550311627" y="873.2587172535104"/>
        <omgdi:waypoint x="335.1370497883181" y="960.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s4f88b6bed" id="BPMNEdge_s4f88b6bed">
        <omgdi:waypoint x="635.0" y="1019.9418637890265"/>
        <omgdi:waypoint x="635.0" y="1082.5000657141247"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s46c6e5abd" id="BPMNEdge_s46c6e5abd">
        <omgdi:waypoint x="654.9458454802094" y="725.0"/>
        <omgdi:waypoint x="825.0" y="725.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s4e5a1f959" id="BPMNEdge_s4e5a1f959">
        <omgdi:waypoint x="875.0" y="329.95000000000005"/>
        <omgdi:waypoint x="875.0" y="425.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s564ac9350" id="BPMNEdge_s564ac9350">
        <omgdi:waypoint x="384.9499999995992" y="445.0"/>
        <omgdi:waypoint x="615.0" y="445.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s6eef9e17c" id="BPMNEdge_s6eef9e17c">
        <omgdi:waypoint x="684.9499999999477" y="290.0"/>
        <omgdi:waypoint x="825.0" y="290.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s10834449f" id="BPMNEdge_s10834449f">
        <omgdi:waypoint x="635.0" y="1162.4500657141248"/>
        <omgdi:waypoint x="635.0" y="1328.0"/>
        <omgdi:waypoint x="493.25104676062233" y="1328.0"/>
        <omgdi:waypoint x="493.25104676062233" y="1396.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s6cedab44c" id="BPMNEdge_s6cedab44c">
        <omgdi:waypoint x="615.0" y="1000.0"/>
        <omgdi:waypoint x="493.25104676062233" y="1000.0"/>
        <omgdi:waypoint x="493.25104676062233" y="1396.0"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s23fc10e84" id="BPMNEdge_s23fc10e84">
        <omgdi:waypoint x="875.0" y="873.6963313421859"/>
        <omgdi:waypoint x="875.0" y="1122.0"/>
        <omgdi:waypoint x="684.9499999999894" y="1122.3958853570155"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="s9cc408cdd" id="BPMNEdge_s9cc408cdd">
        <omgdi:waypoint x="875.0" y="764.9499999999999"/>
        <omgdi:waypoint x="875.0" y="833.7500485032825"/>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="sbba78a532" id="BPMNEdge_sbba78a532">
        <omgdi:waypoint x="354.44399750564924" y="854.2500485032825"/>
        <omgdi:waypoint x="493.25104676062233" y="854.2500485032825"/>
        <omgdi:waypoint x="493.25104676062233" y="1396.0"/>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>